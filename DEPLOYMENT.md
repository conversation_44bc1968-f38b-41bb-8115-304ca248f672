# Deployment Guide

This guide covers deployment options for the Bitcoin Museum 3D experience.

## 🚀 Quick Deployment Options

### 1. GitHub Pages (Recommended for Open Source)

1. **Fork/Clone the repository**
   ```bash
   git clone https://github.com/yourusername/bitcoin-museum.git
   cd bitcoin-museum
   ```

2. **Enable GitHub Pages**
   - Go to repository Settings
   - Scroll to "Pages" section
   - Select "Deploy from a branch"
   - Choose "main" branch and "/ (root)"
   - Save settings

3. **Access your museum**
   - URL: `https://yourusername.github.io/bitcoin-museum`
   - Updates automatically on push to main branch

### 2. Netlify (Recommended for Production)

1. **Connect repository**
   - Sign up at [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub repository

2. **Configure build settings**
   - Build command: (leave empty)
   - Publish directory: (leave empty or set to `/`)
   - No build process needed for static site

3. **Deploy**
   - Automatic deployment on every commit
   - Custom domain support available
   - SSL certificate included

### 3. Vercel

1. **Import project**
   - Sign up at [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import from GitHub

2. **Configure**
   - Framework Preset: Other
   - Build Command: (leave empty)
   - Output Directory: (leave empty)

3. **Deploy**
   - Instant global deployment
   - Automatic HTTPS
   - Edge network optimization

### 4. Traditional Web Hosting

1. **Prepare files**
   ```bash
   # Create deployment package
   zip -r bitcoin-museum.zip . -x "*.git*" "*.md" "node_modules/*"
   ```

2. **Upload to web server**
   - Upload all files to web root directory
   - Ensure web server supports static file serving
   - Configure MIME types for .js files if needed

3. **Configure web server**
   ```apache
   # .htaccess for Apache
   <IfModule mod_mime.c>
       AddType application/javascript .js
       AddType application/json .json
   </IfModule>
   
   # Enable compression
   <IfModule mod_deflate.c>
       AddOutputFilterByType DEFLATE text/html text/css application/javascript
   </IfModule>
   ```

## 🔧 Production Optimizations

### 1. Asset Optimization

#### Minify JavaScript (Optional)
```bash
# Using Terser
npm install -g terser
terser js/MuseumApp.js -o js/MuseumApp.min.js -c -m
```

#### Optimize Images
- Use WebP format for better compression
- Implement lazy loading for exhibit images
- Consider CDN for image delivery

#### Enable Compression
```nginx
# Nginx configuration
gzip on;
gzip_types text/css application/javascript application/json;
gzip_min_length 1000;
```

### 2. Performance Configuration

#### Cache Headers
```apache
# Apache .htaccess
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
</IfModule>
```

#### Content Security Policy
```html
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' https://unpkg.com;
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: https:;
    connect-src 'self';
">
```

### 3. Environment Configuration

#### Production Environment Variables
```javascript
// config/production.js
export const config = {
    environment: 'production',
    enableDebug: false,
    enableStats: false,
    maxQuality: 1.0,
    enableAnalytics: true
};
```

## 📊 Monitoring & Analytics

### 1. Performance Monitoring

#### Web Vitals
```javascript
// Add to index.html
import {getCLS, getFID, getFCP, getLCP, getTTFB} from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

#### Custom Performance Tracking
```javascript
// In PerformanceManager.js
if (config.enableAnalytics) {
    // Track performance metrics
    analytics.track('museum_performance', {
        fps: this.stats.fps,
        loadTime: this.loadTime,
        deviceType: this.detectDeviceType()
    });
}
```

### 2. Error Tracking

#### Sentry Integration
```javascript
import * as Sentry from "@sentry/browser";

Sentry.init({
    dsn: "YOUR_SENTRY_DSN",
    environment: "production"
});
```

#### Custom Error Handling
```javascript
window.addEventListener('error', (event) => {
    console.error('Museum Error:', event.error);
    // Send to analytics service
});
```

## 🔒 Security Considerations

### 1. HTTPS Requirements
- WebGL requires HTTPS in production
- Pointer Lock API requires secure context
- Audio context requires user interaction

### 2. Content Security Policy
```html
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' https://unpkg.com;
    worker-src 'self';
    img-src 'self' data: https:;
">
```

### 3. CORS Configuration
```javascript
// If serving assets from CDN
const corsHeaders = {
    'Access-Control-Allow-Origin': 'https://yourdomain.com',
    'Access-Control-Allow-Methods': 'GET',
    'Access-Control-Allow-Headers': 'Content-Type'
};
```

## 🌐 CDN Integration

### 1. Three.js from CDN
```html
<!-- Use CDN for Three.js in production -->
<script type="importmap">
{
    "imports": {
        "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
        "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
    }
}
</script>
```

### 2. Asset CDN
```javascript
// Configure asset URLs for CDN
const assetBaseURL = 'https://cdn.yourdomain.com/bitcoin-museum/';
const textureLoader = new THREE.TextureLoader();
textureLoader.setPath(assetBaseURL + 'textures/');
```

## 📱 Mobile Optimization

### 1. Responsive Design
```css
@media (max-width: 768px) {
    .navigation-ui {
        bottom: 10px;
        left: 10px;
        right: 10px;
    }
    
    .exhibit-info {
        width: 90vw;
        height: 70vh;
    }
}
```

### 2. Touch Controls
```javascript
// Enable touch controls for mobile
if ('ontouchstart' in window) {
    this.enableTouchControls();
}
```

### 3. Performance Scaling
```javascript
// Reduce quality on mobile devices
const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
if (isMobile) {
    this.performanceManager.qualityLevel = 0.7;
    this.performanceManager.settings.maxDrawDistance = 100;
}
```

## 🧪 Testing in Production

### 1. Browser Testing
- Chrome 80+ (primary target)
- Firefox 75+
- Safari 13+
- Edge 80+

### 2. Device Testing
- Desktop: 1920x1080, 2560x1440, 4K
- Tablet: iPad, Android tablets
- Mobile: iPhone, Android phones

### 3. Performance Testing
```javascript
// Performance benchmarks
const benchmarks = {
    minFPS: 30,
    maxLoadTime: 5000,
    maxMemoryUsage: 500 // MB
};
```

## 🚨 Troubleshooting

### Common Issues

#### 1. CORS Errors
```
Solution: Serve from web server, not file:// protocol
```

#### 2. WebGL Context Lost
```javascript
renderer.domElement.addEventListener('webglcontextlost', (event) => {
    event.preventDefault();
    console.log('WebGL context lost');
    // Implement recovery logic
});
```

#### 3. Mobile Performance
```javascript
// Detect and handle low-end devices
if (renderer.capabilities.maxTextureSize < 2048) {
    // Reduce texture quality
    this.performanceManager.qualityLevel = 0.5;
}
```

### Performance Issues
1. Check browser developer tools
2. Monitor F3 performance stats
3. Reduce quality settings
4. Enable LOD system

### Accessibility Issues
1. Test with screen readers
2. Verify keyboard navigation
3. Check color contrast
4. Test with reduced motion

## 📈 Scaling Considerations

### 1. Traffic Handling
- Use CDN for static assets
- Implement caching strategies
- Consider serverless deployment

### 2. Feature Scaling
- Modular architecture supports easy additions
- Plugin system for custom exhibits
- API integration for dynamic content

### 3. Performance Scaling
- Automatic quality adjustment
- LOD system for complex scenes
- Progressive loading strategies

---

**Ready to deploy your Bitcoin Museum? Choose the option that best fits your needs and follow the steps above!**
