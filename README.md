# Bitcoin Museum - Interactive 3D Experience

A comprehensive, immersive 3D museum showcasing the complete history of Bitcoin from 2008 to 2025. Built with Three.js and modern web technologies.

## 🌟 Features

### 🏛️ Museum Architecture
- **Multi-room galleries** organized by Bitcoin eras
- **Thematic room design** with era-specific lighting and decorations
- **Entrance hall** with welcome experience
- **Transition corridors** connecting different time periods
- **Special exhibition spaces** for future developments

### 🎨 Advanced 3D Graphics
- **Enhanced materials** with PBR (Physically Based Rendering)
- **Dynamic lighting systems** with dramatic spotlights and ambient lighting
- **Particle effects** for special exhibits
- **LOD (Level of Detail)** system for performance optimization
- **Environmental mapping** for realistic reflections

### 🧭 Navigation & User Experience
- **Guided tours** with automatic waypoint navigation
- **Interactive mini-map** showing museum layout and player position
- **Wayfinding arrows** for guided navigation
- **Progress tracking** during tours
- **Keyboard shortcuts** for quick access to features

### ♿ Accessibility Features
- **Screen reader support** with audio announcements
- **Keyboard navigation** for all interactive elements
- **High contrast mode** for visual accessibility
- **Reduced motion options** for motion sensitivity
- **Audio feedback** with focus and activation sounds
- **Font size adjustment** for better readability

### ⚡ Performance Optimization
- **Frustum culling** to hide off-screen objects
- **Distance culling** for far objects
- **Adaptive quality** based on performance
- **Performance monitoring** with real-time stats
- **Memory management** and optimization

### 📚 Historical Content
- **Comprehensive timeline** from 2008-2025
- **Detailed exhibit information** with rich content
- **Interactive displays** with multimedia elements
- **Search functionality** for finding specific events
- **Educational context** for each historical moment

## 🚀 Getting Started

### Prerequisites
- Modern web browser with WebGL support
- Local web server (for development)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/bitcoin-museum.git
   cd bitcoin-museum
   ```

2. **Start a local web server**
   ```bash
   # Using Python 3
   python -m http.server 8000
   
   # Using Node.js (if you have http-server installed)
   npx http-server
   
   # Using PHP
   php -S localhost:8000
   ```

3. **Open in browser**
   Navigate to `http://localhost:8000`

## 🎮 Controls

### Basic Movement
- **W, A, S, D** - Move forward, left, backward, right
- **Mouse** - Look around
- **Shift** - Sprint
- **Space** - Jump
- **Click** - Lock/unlock mouse cursor

### Navigation Features
- **T** - Toggle guided tour
- **N** - Next waypoint (during tour)
- **M** - Toggle mini-map
- **H** - Toggle help panel

### Accessibility
- **Tab** - Navigate between exhibits
- **Enter/Space** - Activate focused exhibit
- **Ctrl+R** - Read current location
- **Ctrl+I** - Read current exhibit info
- **Ctrl+A** - Open accessibility panel
- **F3** - Toggle performance stats

## 🏗️ Architecture

### Core Components

#### MuseumEngine (`js/core/MuseumEngine.js`)
- Three.js scene management
- Renderer and camera setup
- Physics and collision detection
- Animation loop

#### MuseumLayout (`js/core/MuseumLayout.js`)
- Room generation and management
- Architectural structure
- Lighting systems
- Decorative elements

#### ExhibitManager (`js/core/ExhibitManager.js`)
- Exhibit creation and positioning
- 3D asset management
- Material and geometry systems
- Animation handling

#### NavigationSystem (`js/ui/NavigationSystem.js`)
- Guided tour functionality
- Waypoint management
- Mini-map rendering
- User interface controls

#### PerformanceManager (`js/core/PerformanceManager.js`)
- Performance monitoring
- LOD system implementation
- Culling optimizations
- Adaptive quality control

#### AccessibilityManager (`js/ui/AccessibilityManager.js`)
- Screen reader support
- Keyboard navigation
- Audio feedback
- Accessibility options

### Data Structure

#### Exhibit Data (`js/data/exhibitData.js`)
- Historical events organized by year
- 3D model specifications
- Positioning and rotation data
- Special exhibit flags

#### Exhibit Content (`js/data/exhibitContent.js`)
- Detailed historical information
- Rich text content with HTML
- Interactive elements
- Search functionality

## 🎨 Customization

### Adding New Exhibits

1. **Add to exhibit data**
   ```javascript
   // In js/data/exhibitData.js
   2026: [
       {
           name: "New Bitcoin Development",
           infoId: "info-2026-development",
           geometryName: "commonBoxGeo",
           materialName: "orangeMat",
           isSpecial: false
       }
   ]
   ```

2. **Add content information**
   ```javascript
   // In js/data/exhibitContent.js
   "info-2026-development": {
       title: "New Bitcoin Development (2026)",
       content: `<p>Description of the new development...</p>`
   }
   ```

3. **Add HTML info panel**
   ```html
   <!-- In index.html -->
   <div id="info-2026-development" class="exhibit-info"></div>
   ```

### Creating Custom Materials

```javascript
// In ExhibitManager.js
const customMaterial = new THREE.MeshStandardMaterial({
    color: 0xff6b6b,
    metalness: 0.8,
    roughness: 0.2,
    emissive: 0x330000,
    emissiveIntensity: 0.1
});
```

### Adding New Room Types

```javascript
// In MuseumLayout.js
const newRoomConfig = {
    id: "special_room",
    name: "Special Exhibition",
    position: { x: 0, z: -100 },
    size: { width: 40, depth: 30 },
    type: "special",
    theme: "future",
    color: 0x8e44ad
};
```

## 🔧 Development

### Project Structure
```
bitcoin-museum/
├── index.html              # Main HTML file
├── css/
│   └── styles.css          # Styling and UI
├── js/
│   ├── MuseumApp.js        # Main application class
│   ├── core/               # Core engine components
│   │   ├── MuseumEngine.js
│   │   ├── MuseumLayout.js
│   │   ├── ExhibitManager.js
│   │   └── PerformanceManager.js
│   ├── ui/                 # User interface components
│   │   ├── NavigationSystem.js
│   │   └── AccessibilityManager.js
│   └── data/               # Data and content
│       ├── exhibitData.js
│       └── exhibitContent.js
└── README.md
```

### Code Style
- ES6+ modules with import/export
- Comprehensive JSDoc comments
- Consistent naming conventions
- Error handling and logging
- Performance-conscious coding

### Testing
- Manual testing across different browsers
- Performance testing on various devices
- Accessibility testing with screen readers
- Mobile responsiveness testing

## 🌐 Browser Support

- **Chrome** 80+ (recommended)
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+

### WebGL Requirements
- WebGL 1.0 or higher
- Hardware acceleration enabled
- Minimum 1GB GPU memory recommended

## 📱 Mobile Support

The museum is optimized for desktop but includes mobile adaptations:
- Touch controls for movement
- Responsive UI elements
- Performance optimizations for mobile GPUs
- Simplified graphics on lower-end devices

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Areas for Contribution
- Additional historical content
- New 3D models and assets
- Performance optimizations
- Accessibility improvements
- Mobile experience enhancements
- Internationalization

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Satoshi Nakamoto** - For creating Bitcoin
- **Three.js community** - For the amazing 3D library
- **Bitcoin community** - For the rich history to showcase
- **Web accessibility community** - For accessibility guidelines

## 📞 Support

For questions, issues, or suggestions:
- Open an issue on GitHub
- Check the documentation
- Review the code comments

---

**Experience the complete history of Bitcoin in an immersive 3D environment!**
