/**
 * MuseumApp - Main application controller for the Bitcoin Museum
 * Orchestrates all components and manages the overall application lifecycle
 */

import { MuseumEngine } from './core/MuseumEngine.js';
import { MuseumLayout } from './core/MuseumLayout.js';
import { ExhibitManager } from './core/ExhibitManager.js';
import { NavigationSystem } from './ui/NavigationSystem.js';
import { PerformanceManager } from './core/PerformanceManager.js';
import { AccessibilityManager } from './ui/AccessibilityManager.js';
import { injectExhibitContent } from './data/exhibitContent.js';

export class MuseumApp {
    constructor() {
        // Core components
        this.engine = null;
        this.layout = null;
        this.exhibitManager = null;
        this.navigationSystem = null;
        this.performanceManager = null;
        this.accessibilityManager = null;
        
        // Application state
        this.isInitialized = false;
        this.isRunning = false;
        this.loadingProgress = 0;
        
        // DOM elements
        this.blocker = null;
        this.instructionsContainer = null;
        this.startButton = null;
        this.loadingStatus = null;
        this.canvasContainer = null;
        
        // Bind methods
        this.onStartClick = this.onStartClick.bind(this);
        this.onPointerLockChange = this.onPointerLockChange.bind(this);
        this.onPointerLockError = this.onPointerLockError.bind(this);
        this.updateLoadingStatus = this.updateLoadingStatus.bind(this);
    }

    /**
     * Initialize the application
     */
    async initialize() {
        try {
            console.log('Initializing Bitcoin Museum...');

            // Get DOM elements
            this.getDOMElements();

            // Inject exhibit content
            injectExhibitContent();

            // Setup event listeners
            this.setupEventListeners();

            // Initialize core components
            await this.initializeComponents();

            this.isInitialized = true;
            console.log('Bitcoin Museum initialized successfully');

            // Hide loading status and show instructions again
            this.hideLoadingStatus();

        } catch (error) {
            console.error('Failed to initialize Bitcoin Museum:', error);
            this.showError('Failed to initialize the museum. Please refresh the page and try again.');
            throw error;
        }
    }

    /**
     * Get required DOM elements
     */
    getDOMElements() {
        this.blocker = document.getElementById('blocker');
        this.instructionsContainer = document.getElementById('instructions-container');
        this.startButton = document.getElementById('start-museum-btn');
        this.canvasContainer = document.getElementById('canvas-container');
        
        if (!this.blocker || !this.instructionsContainer || !this.startButton || !this.canvasContainer) {
            throw new Error('Required DOM elements not found');
        }
        
        // Create loading status element if it doesn't exist
        this.loadingStatus = document.getElementById('loading-status');
        if (!this.loadingStatus) {
            this.loadingStatus = document.createElement('div');
            this.loadingStatus.id = 'loading-status';
            this.loadingStatus.style.display = 'none';
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Start button click
        this.startButton.addEventListener('click', this.onStartClick);
        
        // Pointer lock events
        document.addEventListener('pointerlockchange', this.onPointerLockChange);
        document.addEventListener('pointerlockerror', this.onPointerLockError);
        
        // Escape key to exit pointer lock
        document.addEventListener('keydown', (event) => {
            if (event.code === 'Escape' && this.engine && this.engine.controls) {
                if (this.engine.controls.isLocked) {
                    this.showUI();
                }
            }
        });
    }

    /**
     * Initialize core components
     */
    async initializeComponents() {
        // Initialize 3D engine
        this.updateLoadingStatus('Initializing 3D engine...');
        this.engine = new MuseumEngine();
        this.engine.onLoadProgress = this.updateLoadingStatus;
        this.engine.onError = (error) => this.showError(error.message);

        // Initialize layout manager
        this.updateLoadingStatus('Setting up museum layout...');
        this.layout = new MuseumLayout(this.engine);

        // Initialize exhibit manager
        this.updateLoadingStatus('Preparing exhibits...');
        this.exhibitManager = new ExhibitManager(this.engine, this.layout);
    }

    /**
     * Start the museum experience
     */
    async start() {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }
            
            this.updateLoadingStatus('Starting museum experience...');
            
            // Initialize the 3D engine
            await this.engine.initialize(this.canvasContainer);
            
            // Create museum layout
            this.updateLoadingStatus('Building museum structure...');
            this.layout.createLayout();
            
            // Create all exhibits
            this.updateLoadingStatus('Setting up exhibits...');
            this.exhibitManager.createAllExhibits();

            // Initialize navigation system
            this.updateLoadingStatus('Setting up navigation...');
            this.navigationSystem = new NavigationSystem(this.engine, this.layout, this.exhibitManager);

            // Initialize performance manager
            this.updateLoadingStatus('Optimizing performance...');
            this.performanceManager = new PerformanceManager(this.engine);

            // Initialize accessibility manager
            this.updateLoadingStatus('Setting up accessibility...');
            this.accessibilityManager = new AccessibilityManager(this.engine, this.navigationSystem);

            // Start the engine
            this.engine.start();

            // Setup update loop for managers
            this.setupUpdateLoop();
            
            this.isRunning = true;
            this.hideUI();
            
            // Lock pointer after a short delay
            setTimeout(() => {
                if (this.engine.controls) {
                    this.engine.controls.lock();
                }
            }, 100);
            
            console.log('Museum started successfully');
            
        } catch (error) {
            console.error('Failed to start museum:', error);
            this.showError('Failed to start the museum. Please check your browser compatibility and try again.');
        }
    }

    /**
     * Setup the update loop for managers
     */
    setupUpdateLoop() {
        const update = () => {
            if (!this.isRunning) return;
            
            // Update exhibit manager
            if (this.exhibitManager) {
                this.exhibitManager.update();
            }

            // Update navigation system
            if (this.navigationSystem) {
                this.navigationSystem.update();
            }

            // Update performance manager
            if (this.performanceManager) {
                this.performanceManager.update();
            }

            // Update accessibility manager
            if (this.accessibilityManager) {
                this.accessibilityManager.update();
            }
            
            requestAnimationFrame(update);
        };
        
        update();
    }

    /**
     * Handle start button click
     */
    async onStartClick() {
        if (this.isRunning) {
            // If already running, just lock the pointer
            if (this.engine.controls && !this.engine.controls.isLocked) {
                this.engine.controls.lock();
            }
            return;
        }
        
        // Start the museum
        await this.start();
    }

    /**
     * Handle pointer lock change
     */
    onPointerLockChange() {
        if (document.pointerLockElement === this.engine?.renderer?.domElement) {
            // Pointer is locked
            this.hideUI();
        } else {
            // Pointer is unlocked
            this.showUI();
        }
    }

    /**
     * Handle pointer lock error
     */
    onPointerLockError() {
        console.error('Pointer lock failed');
        this.showError('Failed to lock mouse pointer. Please try again.');
    }

    /**
     * Show the UI overlay
     */
    showUI() {
        if (this.blocker) {
            this.blocker.style.display = 'flex';
        }
        
        // Update button text if museum is running
        if (this.isRunning && this.startButton) {
            this.startButton.textContent = 'Continue Exploring';
        }
    }

    /**
     * Hide the UI overlay
     */
    hideUI() {
        if (this.blocker) {
            this.blocker.style.display = 'none';
        }
    }

    /**
     * Update loading status
     */
    updateLoadingStatus(message) {
        console.log('Museum:', message);

        if (this.loadingStatus) {
            this.loadingStatus.textContent = message;
            this.loadingStatus.style.display = 'block';
        }

        // Hide instructions and show loading status
        if (this.instructionsContainer) {
            this.instructionsContainer.style.display = 'none';
        }

        if (this.blocker && !this.loadingStatus.parentNode) {
            this.blocker.appendChild(this.loadingStatus);
        }
    }

    /**
     * Hide loading status and show instructions
     */
    hideLoadingStatus() {
        if (this.loadingStatus) {
            this.loadingStatus.style.display = 'none';
        }

        // Show instructions container again
        if (this.instructionsContainer) {
            this.instructionsContainer.style.display = 'flex';
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('Museum Error:', message);
        
        if (this.loadingStatus) {
            this.loadingStatus.textContent = `Error: ${message}`;
            this.loadingStatus.style.color = '#ef4444';
        }
        
        // Show instructions container again
        if (this.instructionsContainer) {
            this.instructionsContainer.style.display = 'flex';
        }
        
        // Update button to retry
        if (this.startButton) {
            this.startButton.textContent = 'Retry';
        }
    }

    /**
     * Stop the museum
     */
    stop() {
        this.isRunning = false;
        
        if (this.engine) {
            this.engine.stop();
        }
        
        this.showUI();
    }

    /**
     * Cleanup and dispose of resources
     */
    dispose() {
        this.stop();
        
        if (this.engine) {
            this.engine.dispose();
        }

        if (this.performanceManager) {
            this.performanceManager.dispose();
        }

        if (this.accessibilityManager) {
            this.accessibilityManager.dispose();
        }
        
        // Remove event listeners
        this.startButton?.removeEventListener('click', this.onStartClick);
        document.removeEventListener('pointerlockchange', this.onPointerLockChange);
        document.removeEventListener('pointerlockerror', this.onPointerLockError);
    }

    /**
     * Get current museum statistics
     */
    getStatistics() {
        return {
            isInitialized: this.isInitialized,
            isRunning: this.isRunning,
            totalExhibits: this.exhibitManager?.exhibits?.length || 0,
            totalRooms: this.layout?.rooms?.size || 0,
            engineInfo: {
                scene: this.engine?.scene ? 'Loaded' : 'Not loaded',
                renderer: this.engine?.renderer ? 'Active' : 'Inactive',
                controls: this.engine?.controls?.isLocked ? 'Locked' : 'Unlocked'
            },
            performance: this.performanceManager?.stats || {},
            accessibility: {
                screenReaderMode: this.accessibilityManager?.isScreenReaderMode || false,
                highContrastMode: this.accessibilityManager?.isHighContrastMode || false,
                keyboardNavigation: this.accessibilityManager?.keyboardNavigationEnabled || false
            }
        };
    }

    /**
     * Navigate to a specific year or exhibit
     */
    navigateToYear(year) {
        // This will be implemented in future iterations
        console.log(`Navigation to year ${year} requested`);
    }

    /**
     * Navigate to a specific exhibit
     */
    navigateToExhibit(exhibitName) {
        if (!this.exhibitManager) return;
        
        const exhibit = this.exhibitManager.getExhibitByName(exhibitName);
        if (exhibit && this.engine?.controls) {
            const targetPosition = exhibit.mesh.position.clone();
            targetPosition.y = this.engine.playerHeight;
            targetPosition.z += 5; // Stand a bit away from the exhibit
            
            this.engine.controls.getObject().position.copy(targetPosition);
            console.log(`Navigated to exhibit: ${exhibitName}`);
        }
    }

    /**
     * Toggle audio (placeholder for future audio implementation)
     */
    toggleAudio() {
        // This will be implemented in future iterations
        console.log('Audio toggle requested');
    }

    /**
     * Get help information
     */
    getHelp() {
        return {
            controls: {
                'W, A, S, D': 'Move around',
                'Mouse': 'Look around',
                'Shift': 'Sprint',
                'Space': 'Jump',
                'Escape': 'Exit pointer lock'
            },
            features: {
                'Proximity Detection': 'Walk close to exhibits to see information',
                'Special Exhibits': 'Look for glowing exhibits with images',
                'Room Navigation': 'Explore different eras chronologically'
            }
        };
    }
}

// Export for manual initialization
// The MuseumApp should be manually initialized by the HTML page
