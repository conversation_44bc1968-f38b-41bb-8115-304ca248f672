/**
 * ExhibitManager - Manages all exhibits and their interactions
 * Handles exhibit creation, positioning, animations, and information display
 */

import * as THREE from 'three';
import { bitcoinEventsData } from '../data/exhibitData.js';

export class ExhibitManager {
    constructor(engine, layout) {
        this.engine = engine;
        this.layout = layout;
        this.exhibits = [];
        this.geometries = {};
        this.materials = {};
        this.currentExhibitInfo = null;
        this.interactionDistance = 6.5;
        
        this.initializeGeometries();
        this.initializeMaterials();
    }

    /**
     * Initialize all 3D geometries used for exhibits
     */
    initializeGeometries() {
        this.geometries = {
            // Basic shapes
            commonBoxGeo: new THREE.BoxGeometry(1.5, 1.5, 1.5),
            sphereGeo: new THREE.SphereGeometry(0.8, 16, 12),
            cylinderGeo: new THREE.CylinderGeometry(0.6, 0.6, 1.5, 12),
            coneGeo: new THREE.ConeGeometry(0.8, 1.8, 8),
            torusGeo: new THREE.TorusGeometry(0.7, 0.3, 8, 16),
            octahedronGeo: new THREE.OctahedronGeometry(1),
            dodecahedronGeo: new THREE.DodecahedronGeometry(0.8),
            torusKnotGeo: new THREE.TorusKnotGeometry(0.6, 0.2, 64, 8),
            
            // Specialized shapes
            paperGeo: new THREE.BoxGeometry(1.2, 0.1, 1.6),
            pizzaGeo: new THREE.CylinderGeometry(1.2, 1.2, 0.2, 8),
            atmGeo: new THREE.BoxGeometry(1, 2.5, 0.8),
            bookGeo: new THREE.BoxGeometry(0.8, 1.2, 0.15),
            
            // Special event geometries
            priceConeGeo: new THREE.ConeGeometry(1.2, 3, 8),
            athConeGeo: new THREE.ConeGeometry(1.5, 4, 8),
            mtGoxCollapseGeo: new THREE.BoxGeometry(2, 0.5, 2),
            ftxCollapseGeo: new THREE.BoxGeometry(2.5, 0.3, 2.5),
            
            // Protocol and technical
            lnGeo: new THREE.IcosahedronGeometry(1, 1),
            taprootKnotGeo: new THREE.TorusKnotGeometry(0.8, 0.25, 100, 16),
            blockDebateGeo: new THREE.BoxGeometry(2, 1, 1),
            
            // Flags and symbols
            elSalvadorFlagGeo: new THREE.PlaneGeometry(2, 1.3),
            buySignGeo: new THREE.PlaneGeometry(1.8, 1.2),
            
            // Future and special
            futureSphereGeo: new THREE.IcosahedronGeometry(1.2, 2),
            maximalismSphereGeo: new THREE.SphereGeometry(1, 32, 16),
            
            // Mining and technical
            iceGeo: new THREE.IcosahedronGeometry(1, 0),
            skullGeo: new THREE.SphereGeometry(0.9, 16, 12),
            
            // Financial instruments
            spotETFGeo: new THREE.BoxGeometry(1.8, 0.3, 1.2),
            ledgerCylinderGeo: new THREE.CylinderGeometry(0.4, 0.4, 1.2, 16),
            
            // Protocols and innovations
            runesGeo: new THREE.OctahedronGeometry(1.1),
            microBTCIconGeo: new THREE.BoxGeometry(1.3, 1.3, 0.2),
            politicsGeo: new THREE.CylinderGeometry(1, 0.8, 1.5, 6),
            
            // Large display geometries
            planeGeoLarge: new THREE.PlaneGeometry(3, 2),
            capsuleGeo: new THREE.CapsuleGeometry(0.5, 1.5, 4, 8),

            // Additional missing geometries
            // These will be created as basic shapes for now
            // Can be replaced with more detailed models later
        };

        // Add any missing geometries as basic shapes
        const missingGeometries = [
            'paperGeo', 'pizzaGeo', 'atmGeo', 'priceConeGeo', 'blockDebateGeo',
            'mtGoxCollapseGeo', 'lnGeo', 'iceGeo', 'buySignGeo', 'ftxCollapseGeo',
            'skullGeo', 'spotETFGeo', 'bookGeo', 'elSalvadorFlagGeo', 'athConeGeo',
            'taprootKnotGeo', 'microBTCIconGeo', 'politicsGeo', 'runesGeo',
            'maximalismSphereGeo', 'futureSphereGeo', 'ledgerCylinderGeo'
        ];

        // Ensure all referenced geometries exist
        missingGeometries.forEach(geoName => {
            if (!this.geometries[geoName]) {
                // Create a default geometry based on the name
                if (geoName.includes('Cone') || geoName.includes('cone')) {
                    this.geometries[geoName] = new THREE.ConeGeometry(0.8, 1.8, 8);
                } else if (geoName.includes('Sphere') || geoName.includes('sphere')) {
                    this.geometries[geoName] = new THREE.SphereGeometry(0.8, 16, 12);
                } else if (geoName.includes('Cylinder') || geoName.includes('cylinder')) {
                    this.geometries[geoName] = new THREE.CylinderGeometry(0.6, 0.6, 1.5, 12);
                } else if (geoName.includes('Plane') || geoName.includes('plane') || geoName.includes('Flag') || geoName.includes('Sign')) {
                    this.geometries[geoName] = new THREE.PlaneGeometry(1.5, 1);
                } else if (geoName.includes('Knot') || geoName.includes('knot')) {
                    this.geometries[geoName] = new THREE.TorusKnotGeometry(0.6, 0.2, 64, 8);
                } else if (geoName.includes('Torus') || geoName.includes('torus')) {
                    this.geometries[geoName] = new THREE.TorusGeometry(0.7, 0.3, 8, 16);
                } else {
                    // Default to box geometry
                    this.geometries[geoName] = new THREE.BoxGeometry(1.5, 1.5, 1.5);
                }
            }
        });
    }

    /**
     * Initialize all materials used for exhibits
     */
    initializeMaterials() {
        // Create texture loader for enhanced materials
        const textureLoader = this.engine.textureLoader;

        this.materials = {
            // Enhanced basic colors with PBR properties
            orangeMat: new THREE.MeshStandardMaterial({
                color: 0xf39c12,
                metalness: 0.4,
                roughness: 0.3,
                emissive: 0x331a00,
                emissiveIntensity: 0.1
            }),
            blueMat: new THREE.MeshStandardMaterial({
                color: 0x3498db,
                metalness: 0.3,
                roughness: 0.4
            }),
            redMat: new THREE.MeshStandardMaterial({
                color: 0xe74c3c,
                metalness: 0.2,
                roughness: 0.5,
                emissive: 0x330000,
                emissiveIntensity: 0.05
            }),
            greenMat: new THREE.MeshStandardMaterial({
                color: 0x2ecc71,
                metalness: 0.3,
                roughness: 0.3
            }),
            greyMat: new THREE.MeshStandardMaterial({
                color: 0x95a5a6,
                metalness: 0.6,
                roughness: 0.4
            }),
            whiteMat: new THREE.MeshStandardMaterial({
                color: 0xecf0f1,
                metalness: 0.1,
                roughness: 0.2
            }),
            darkRedMat: new THREE.MeshStandardMaterial({
                color: 0x8b0000,
                metalness: 0.3,
                roughness: 0.8,
                emissive: 0x220000,
                emissiveIntensity: 0.1
            }),

            // Premium materials for special exhibits
            goldMat: new THREE.MeshStandardMaterial({
                color: 0xffd700,
                metalness: 1.0,
                roughness: 0.1,
                emissive: 0x332200,
                emissiveIntensity: 0.2
            }),

            silverMat: new THREE.MeshStandardMaterial({
                color: 0xc0c0c0,
                metalness: 1.0,
                roughness: 0.15
            }),

            bronzeMat: new THREE.MeshStandardMaterial({
                color: 0xcd7f32,
                metalness: 0.9,
                roughness: 0.2,
                emissive: 0x221100,
                emissiveIntensity: 0.1
            }),

            // Cryptocurrency-specific materials
            litecoinMat: new THREE.MeshStandardMaterial({
                color: 0xbfbfbf,
                metalness: 0.9,
                roughness: 0.1
            }),

            bitcoinOrangeMat: new THREE.MeshStandardMaterial({
                color: 0xf7931a,
                metalness: 0.8,
                roughness: 0.2,
                emissive: 0x331100,
                emissiveIntensity: 0.15
            }),

            // Corporate and institutional materials
            wordpressMat: new THREE.MeshStandardMaterial({
                color: 0x21759b,
                metalness: 0.4,
                roughness: 0.4
            }),
            overstockMat: new THREE.MeshStandardMaterial({
                color: 0xff6900,
                metalness: 0.3,
                roughness: 0.3,
                emissive: 0x331100,
                emissiveIntensity: 0.1
            }),
            geminiMat: new THREE.MeshStandardMaterial({
                color: 0x00d4aa,
                metalness: 0.5,
                roughness: 0.2
            }),
            bchMat: new THREE.MeshStandardMaterial({
                color: 0x8dc351,
                metalness: 0.4,
                roughness: 0.3
            }),

            // Dark/controversial materials
            silkRoadMat: new THREE.MeshStandardMaterial({
                color: 0x2c3e50,
                metalness: 0.2,
                roughness: 0.9,
                emissive: 0x111111,
                emissiveIntensity: 0.05
            }),
            mtGoxMat: new THREE.MeshStandardMaterial({
                color: 0x34495e,
                metalness: 0.3,
                roughness: 0.8,
                emissive: 0x111111,
                emissiveIntensity: 0.1
            }),
            ftxMat: new THREE.MeshStandardMaterial({
                color: 0x000000,
                metalness: 0.2,
                roughness: 0.95,
                emissive: 0x110000,
                emissiveIntensity: 0.05
            }),

            // Technical and protocol materials
            lnGlowMat: new THREE.MeshStandardMaterial({
                color: 0xffff00,
                metalness: 0.2,
                roughness: 0.1,
                emissive: 0x666600,
                emissiveIntensity: 0.3,
                transparent: true,
                opacity: 0.9
            }),

            iceMat: new THREE.MeshStandardMaterial({
                color: 0x87ceeb,
                metalness: 0.1,
                roughness: 0.05,
                transparent: true,
                opacity: 0.7
            }),

            glassMat: new THREE.MeshStandardMaterial({
                color: 0xffffff,
                metalness: 0.0,
                roughness: 0.0,
                transparent: true,
                opacity: 0.3
            }),

            // Cultural and meme materials
            dogeMat: new THREE.MeshStandardMaterial({
                color: 0xdaa520,
                metalness: 0.4,
                roughness: 0.4,
                emissive: 0x221100,
                emissiveIntensity: 0.1
            }),
            signMat: new THREE.MeshStandardMaterial({
                color: 0xf1c40f,
                metalness: 0.3,
                roughness: 0.3,
                emissive: 0x332200,
                emissiveIntensity: 0.15
            }),

            // Regulatory and institutional
            senateMat: new THREE.MeshStandardMaterial({
                color: 0x1e3a8a,
                metalness: 0.5,
                roughness: 0.4
            }),
            bankingCrisisMat: new THREE.MeshStandardMaterial({
                color: 0x7f1d1d,
                metalness: 0.4,
                roughness: 0.7,
                emissive: 0x220000,
                emissiveIntensity: 0.1
            }),

            // Future and innovation
            futureMat: new THREE.MeshStandardMaterial({
                color: 0x8e44ad,
                metalness: 0.6,
                roughness: 0.2,
                emissive: 0x2d1b3d,
                emissiveIntensity: 0.2,
                transparent: true,
                opacity: 0.9
            }),

            hologramMat: new THREE.MeshStandardMaterial({
                color: 0x00ffff,
                metalness: 0.1,
                roughness: 0.1,
                emissive: 0x003333,
                emissiveIntensity: 0.4,
                transparent: true,
                opacity: 0.6
            }),

            defiMat: new THREE.MeshStandardMaterial({
                color: 0x9b59b6,
                metalness: 0.5,
                roughness: 0.3,
                emissive: 0x221133,
                emissiveIntensity: 0.1
            }),

            // Financial products
            spotETFMatGreen: new THREE.MeshStandardMaterial({
                color: 0x2ecc71,
                metalness: 0.7,
                roughness: 0.15
            }),

            // Educational and community
            bookMat: new THREE.MeshStandardMaterial({
                color: 0xe52d27,
                metalness: 0.1,
                roughness: 0.8
            }),

            // Altcoins and ecosystem
            daoMat: new THREE.MeshStandardMaterial({
                color: 0x627eea,
                metalness: 0.4,
                roughness: 0.4,
                emissive: 0x111133,
                emissiveIntensity: 0.1
            }),
            ethMergeMat: new THREE.MeshStandardMaterial({
                color: 0x627eea,
                metalness: 0.5,
                roughness: 0.2
            }),

            // Special effect materials
            skullMat: new THREE.MeshStandardMaterial({
                color: 0x2c2c2c,
                metalness: 0.3,
                roughness: 0.9,
                emissive: 0x111111,
                emissiveIntensity: 0.1
            }),

            maximalismOrangeWireMat: new THREE.MeshStandardMaterial({
                color: 0xf39c12,
                wireframe: true,
                metalness: 0.4,
                roughness: 0.3,
                emissive: 0x331100,
                emissiveIntensity: 0.2
            }),

            neonMat: new THREE.MeshStandardMaterial({
                color: 0xff00ff,
                metalness: 0.1,
                roughness: 0.1,
                emissive: 0x330033,
                emissiveIntensity: 0.5,
                transparent: true,
                opacity: 0.8
            })
        };

        // Add environment mapping for metallic materials
        this.addEnvironmentMapping();
    }

    /**
     * Add environment mapping for realistic reflections
     */
    addEnvironmentMapping() {
        // Create a simple cube texture for environment mapping
        const cubeTextureLoader = new THREE.CubeTextureLoader();

        // For now, we'll use a simple color-based environment
        // In a full implementation, this would load actual environment textures
        const envMapIntensity = 0.5;

        // Apply environment mapping to metallic materials
        const metallicMaterials = [
            'goldMat', 'silverMat', 'bronzeMat', 'litecoinMat',
            'bitcoinOrangeMat', 'geminiMat', 'spotETFMatGreen'
        ];

        metallicMaterials.forEach(matName => {
            if (this.materials[matName]) {
                this.materials[matName].envMapIntensity = envMapIntensity;
            }
        });
    }

    /**
     * Create enhanced particle systems for special exhibits
     */
    createParticleSystem(exhibit) {
        const particleCount = 100;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            positions[i * 3] = (Math.random() - 0.5) * 10;
            positions[i * 3 + 1] = Math.random() * 5;
            positions[i * 3 + 2] = (Math.random() - 0.5) * 10;

            colors[i * 3] = Math.random();
            colors[i * 3 + 1] = Math.random();
            colors[i * 3 + 2] = Math.random();
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const particleMaterial = new THREE.PointsMaterial({
            size: 0.1,
            vertexColors: true,
            transparent: true,
            opacity: 0.6
        });

        const particleSystem = new THREE.Points(particles, particleMaterial);
        particleSystem.position.copy(exhibit.mesh.position);

        this.engine.scene.add(particleSystem);
        exhibit.particleSystem = particleSystem;

        return particleSystem;
    }

    /**
     * Create advanced lighting effects for exhibits
     */
    createAdvancedLighting(exhibit) {
        const { mesh, isSpecial, eventData } = exhibit;

        // Remove old lighting
        const oldLights = mesh.children.filter(child => child.isSpotLight || child.isPointLight);
        oldLights.forEach(light => mesh.remove(light));

        if (isSpecial) {
            // Create dramatic lighting for special exhibits
            this.createDramaticExhibitLighting(exhibit);
        } else {
            // Create standard enhanced lighting
            this.createStandardExhibitLighting(exhibit);
        }

        // Add category-specific lighting effects
        if (eventData.category) {
            this.addCategoryLighting(exhibit, eventData.category);
        }
    }

    /**
     * Create dramatic lighting for special exhibits
     */
    createDramaticExhibitLighting(exhibit) {
        const { mesh } = exhibit;

        // Main spotlight
        const mainSpot = new THREE.SpotLight(0xffffff, 200, 30, Math.PI / 6, 0.3, 1);
        mainSpot.position.set(0, 8, 0);
        mainSpot.target = mesh.children[0];
        mainSpot.castShadow = true;
        mainSpot.shadow.mapSize.width = 1024;
        mainSpot.shadow.mapSize.height = 1024;
        mesh.add(mainSpot);

        // Accent lights
        const accentColors = [0xff6b6b, 0x4ecdc4, 0x45b7d1, 0xf9ca24];
        for (let i = 0; i < 4; i++) {
            const angle = (i / 4) * Math.PI * 2;
            const radius = 3;

            const accentLight = new THREE.PointLight(accentColors[i], 50, 15);
            accentLight.position.set(
                Math.cos(angle) * radius,
                2,
                Math.sin(angle) * radius
            );
            mesh.add(accentLight);
        }

        // Rim lighting
        const rimLight = new THREE.SpotLight(0x00ffff, 80, 20, Math.PI / 3, 0.5, 1);
        rimLight.position.set(0, 1, -5);
        rimLight.target = mesh.children[0];
        mesh.add(rimLight);
    }

    /**
     * Create standard enhanced lighting
     */
    createStandardExhibitLighting(exhibit) {
        const { mesh } = exhibit;

        // Main exhibit light
        const mainLight = new THREE.SpotLight(0xffffff, 120, 25, Math.PI / 5, 0.2, 1);
        mainLight.position.set(0, 6, 0);
        mainLight.target = mesh.children[0];
        mainLight.castShadow = true;
        mesh.add(mainLight);

        // Fill light
        const fillLight = new THREE.PointLight(0xffffff, 30, 10);
        fillLight.position.set(2, 3, 2);
        mesh.add(fillLight);
    }

    /**
     * Add category-specific lighting
     */
    addCategoryLighting(exhibit, category) {
        const { mesh } = exhibit;
        const categoryColors = {
            foundation: 0xf59e0b,
            technology: 0x3b82f6,
            economics: 0x10b981,
            adoption: 0x8b5cf6,
            infrastructure: 0x06b6d4,
            mining: 0xf97316,
            regulation: 0xef4444,
            security: 0xdc2626,
            controversy: 0x7c2d12,
            altcoins: 0x64748b,
            protocol: 0x059669,
            governance: 0x7c3aed
        };

        const categoryColor = categoryColors[category] || 0xffffff;

        // Add subtle category-colored ambient light
        const categoryLight = new THREE.PointLight(categoryColor, 20, 8);
        categoryLight.position.set(0, 1, 0);
        mesh.add(categoryLight);
    }

    /**
     * Create all exhibits based on Bitcoin events data
     */
    createAllExhibits() {
        const sortedYears = Object.keys(bitcoinEventsData)
            .map(Number)
            .sort((a, b) => a - b);

        sortedYears.forEach((year, yearIndex) => {
            const eventsForYear = bitcoinEventsData[year];
            if (!eventsForYear) return;

            this.createExhibitsForYear(year, eventsForYear, yearIndex);
        });

        console.log(`Created ${this.exhibits.length} exhibits`);
    }

    /**
     * Create exhibits for a specific year
     */
    createExhibitsForYear(year, events, yearIndex) {
        const roomConfig = this.calculateRoomPosition(yearIndex);
        
        events.forEach((eventData, eventIndex) => {
            const position = this.calculateExhibitPosition(
                roomConfig, 
                events.length, 
                eventIndex
            );
            
            this.createExhibit(eventData, position);
        });
    }

    /**
     * Calculate room position based on year index
     */
    calculateRoomPosition(yearIndex) {
        const YEAR_GROUP_DEPTH_Z = 75;
        const SIDE_A_X_BASE = -this.engine.museumWidth / 4;
        const SIDE_B_X_BASE = this.engine.museumWidth / 4;
        
        const blockGroupIdx = Math.floor(yearIndex / 4);
        const posInBlockGroup = yearIndex % 4;
        
        const groupCenterZ = (this.engine.totalMuseumLengthZ / 2) - 
                           (blockGroupIdx * YEAR_GROUP_DEPTH_Z) - 
                           (YEAR_GROUP_DEPTH_Z / 2);
        
        let baseX, centerZ, rotationY;
        
        if (posInBlockGroup === 0) { // A, top
            baseX = SIDE_A_X_BASE;
            centerZ = groupCenterZ + YEAR_GROUP_DEPTH_Z / 4;
            rotationY = Math.PI / 2;
        } else if (posInBlockGroup === 1) { // B, top
            baseX = SIDE_B_X_BASE;
            centerZ = groupCenterZ + YEAR_GROUP_DEPTH_Z / 4;
            rotationY = -Math.PI / 2;
        } else if (posInBlockGroup === 2) { // B, bottom
            baseX = SIDE_B_X_BASE;
            centerZ = groupCenterZ - YEAR_GROUP_DEPTH_Z / 4;
            rotationY = -Math.PI / 2;
        } else { // A, bottom
            baseX = SIDE_A_X_BASE;
            centerZ = groupCenterZ - YEAR_GROUP_DEPTH_Z / 4;
            rotationY = Math.PI / 2;
        }
        
        return { baseX, centerZ, rotationY };
    }

    /**
     * Calculate individual exhibit position within a room
     */
    calculateExhibitPosition(roomConfig, totalExhibits, exhibitIndex) {
        const EXHIBIT_AREA_WIDTH = (this.engine.museumWidth / 2) * 0.55;
        const EXHIBIT_AREA_DEPTH = 37.5 * 0.6;
        
        const positions = this.getDicePatternPositions(
            totalExhibits, 
            EXHIBIT_AREA_WIDTH, 
            EXHIBIT_AREA_DEPTH
        );
        
        const relativePos = positions[exhibitIndex] || { x: 0, z: 0 };
        
        return {
            x: roomConfig.baseX + relativePos.x,
            y: 0.1, // Base height
            z: roomConfig.centerZ + relativePos.z,
            rotationY: roomConfig.rotationY
        };
    }

    /**
     * Generate dice pattern positions for exhibits
     */
    getDicePatternPositions(numObjects, areaWidth, areaDepth) {
        const positions = [];
        const paddingFactor = 0.15;
        const effectiveWidth = areaWidth * (1 - 2 * paddingFactor);
        const effectiveDepth = areaDepth * (1 - 2 * paddingFactor);
        
        const stepX = effectiveWidth / 3.5;
        const stepZ = effectiveDepth / 3.5;
        
        if (numObjects === 1) {
            positions.push({ x: 0, z: 0 });
        } else if (numObjects === 2) {
            positions.push({ x: -stepX * 0.8, z: -stepZ * 0.8 });
            positions.push({ x: stepX * 0.8, z: stepZ * 0.8 });
        } else if (numObjects === 3) {
            positions.push({ x: -stepX, z: -stepZ });
            positions.push({ x: 0, z: 0 });
            positions.push({ x: stepX, z: stepZ });
        } else if (numObjects === 4) {
            positions.push({ x: -stepX * 0.9, z: -stepZ * 0.9 });
            positions.push({ x: stepX * 0.9, z: -stepZ * 0.9 });
            positions.push({ x: -stepX * 0.9, z: stepZ * 0.9 });
            positions.push({ x: stepX * 0.9, z: stepZ * 0.9 });
        } else if (numObjects === 5) {
            positions.push({ x: -stepX, z: -stepZ });
            positions.push({ x: stepX, z: -stepZ });
            positions.push({ x: 0, z: 0 });
            positions.push({ x: -stepX, z: stepZ });
            positions.push({ x: stepX, z: stepZ });
        } else if (numObjects === 6) {
            const colOffset = stepX * 0.9;
            const rowOffsetOuter = stepZ * 1.1;
            positions.push({ x: -colOffset, z: -rowOffsetOuter });
            positions.push({ x: -colOffset, z: 0 });
            positions.push({ x: -colOffset, z: rowOffsetOuter });
            positions.push({ x: colOffset, z: -rowOffsetOuter });
            positions.push({ x: colOffset, z: 0 });
            positions.push({ x: colOffset, z: rowOffsetOuter });
        } else {
            // Grid layout for more objects
            const cols = Math.ceil(Math.sqrt(numObjects));
            const rows = Math.ceil(numObjects / cols);
            const spacingX = cols > 1 ? effectiveWidth / (cols - 1) : 0;
            const spacingZ = rows > 1 ? effectiveDepth / (rows - 1) : 0;
            const offsetX = cols > 1 ? -effectiveWidth / 2 : 0;
            const offsetZ = rows > 1 ? -effectiveDepth / 2 : 0;
            
            for (let i = 0; i < numObjects; i++) {
                const r = Math.floor(i / cols);
                const c = i % cols;
                positions.push({
                    x: offsetX + c * spacingX,
                    z: offsetZ + r * spacingZ
                });
            }
        }
        
        return positions;
    }

    /**
     * Create a single exhibit with enhanced visuals
     */
    createExhibit(eventData, position) {
        const exhibitGroup = new THREE.Group();
        exhibitGroup.position.set(position.x, position.y, position.z);

        // Get geometry and material with enhancements
        const geometry = this.getEnhancedGeometry(eventData);
        const material = this.getEnhancedMaterial(eventData);

        // Create main mesh
        const mesh = new THREE.Mesh(geometry, material);
        mesh.rotation.y = eventData.rotationY !== undefined ? eventData.rotationY : position.rotationY;
        mesh.castShadow = true;
        mesh.receiveShadow = true;

        // Calculate proper Y position based on geometry
        geometry.computeBoundingBox();
        const geoHeight = geometry.boundingBox.max.y - geometry.boundingBox.min.y;
        mesh.position.y = geoHeight / 2;

        exhibitGroup.add(mesh);

        // Create exhibit object
        const exhibit = {
            mesh: exhibitGroup,
            mainMesh: mesh,
            infoId: eventData.infoId,
            name: eventData.name,
            eventData: eventData,
            isSpecial: eventData.isSpecial || false
        };

        // Add enhanced visual elements
        this.addEnhancedVisuals(exhibit);

        // Add to scene and collections
        this.engine.scene.add(exhibitGroup);
        this.engine.collidableObjects.push(mesh);
        this.exhibits.push(exhibit);
        this.engine.exhibits.push(exhibit);

        // Add to animated objects if needed
        if (this.shouldAnimate(eventData.name)) {
            this.engine.animatedObjects.push(exhibit);
        }

        return exhibit;
    }

    /**
     * Get enhanced geometry based on exhibit data
     */
    getEnhancedGeometry(eventData) {
        let geometry = this.geometries[eventData.geometryName] || this.geometries.commonBoxGeo;

        // Create enhanced versions for special exhibits
        if (eventData.isSpecial) {
            geometry = this.createEnhancedGeometry(eventData);
        }

        return geometry;
    }

    /**
     * Create enhanced geometry for special exhibits
     */
    createEnhancedGeometry(eventData) {
        const baseGeometry = this.geometries[eventData.geometryName] || this.geometries.commonBoxGeo;

        // Add more detail to special exhibits
        switch (eventData.geometryName) {
            case 'commonBoxGeo':
                return new THREE.BoxGeometry(2, 2, 2, 4, 4, 4);
            case 'sphereGeo':
                return new THREE.SphereGeometry(1, 32, 24);
            case 'cylinderGeo':
                return new THREE.CylinderGeometry(0.8, 0.8, 2, 32);
            default:
                return baseGeometry;
        }
    }

    /**
     * Get enhanced material based on exhibit data
     */
    getEnhancedMaterial(eventData) {
        let material = this.materials[eventData.materialName] || this.materials.greyMat;

        // Use premium materials for special exhibits
        if (eventData.isSpecial) {
            material = this.getSpecialMaterial(eventData);
        }

        return material;
    }

    /**
     * Get special material for important exhibits
     */
    getSpecialMaterial(eventData) {
        const specialMaterials = {
            'Genesis Block': this.materials.goldMat,
            'Bitcoin Pizza': this.materials.bitcoinOrangeMat,
            'Erstes Halving': this.materials.silverMat,
            'Zweites Halving': this.materials.silverMat,
            'Drittes Halving': this.materials.silverMat,
            'Viertes Halving': this.materials.silverMat,
            'US Spot ETFs': this.materials.spotETFMatGreen,
            'El Salvador': this.materials.goldMat,
            'Zukunft?': this.materials.futureMat
        };

        return specialMaterials[eventData.name] || this.materials[eventData.materialName] || this.materials.greyMat;
    }

    /**
     * Add enhanced visual elements to exhibits
     */
    addEnhancedVisuals(exhibit) {
        // Add special image plane for special exhibits
        if (exhibit.eventData.isSpecial && exhibit.eventData.imageUrl) {
            this.addEnhancedImagePlane(exhibit);
        }

        // Add advanced lighting
        this.createAdvancedLighting(exhibit);

        // Add particle effects for very special exhibits
        if (this.shouldHaveParticles(exhibit.eventData.name)) {
            this.createParticleSystem(exhibit);
        }

        // Add information display
        this.addInformationDisplay(exhibit);

        // Add interactive elements
        this.addInteractiveElements(exhibit);
    }

    /**
     * Add enhanced image plane with better materials
     */
    addEnhancedImagePlane(exhibit) {
        const { mesh, eventData } = exhibit;

        const planeGeometry = new THREE.PlaneGeometry(4, 3);

        // Create frame for the image
        const frameGeometry = new THREE.BoxGeometry(4.2, 3.2, 0.1);
        const frameMaterial = new THREE.MeshStandardMaterial({
            color: 0x8b4513,
            metalness: 0.8,
            roughness: 0.2
        });
        const frame = new THREE.Mesh(frameGeometry, frameMaterial);
        frame.position.set(0, 1.5, 0.05);
        mesh.add(frame);

        // Create the image plane
        const imageMaterial = new THREE.MeshStandardMaterial({
            map: this.engine.textureLoader.load(
                eventData.imageUrl,
                (texture) => {
                    texture.colorSpace = THREE.SRGBColorSpace;
                },
                undefined,
                (err) => console.error('Failed to load exhibit image:', err)
            ),
            side: THREE.DoubleSide,
            metalness: 0.1,
            roughness: 0.9
        });

        const imagePlane = new THREE.Mesh(planeGeometry, imageMaterial);
        imagePlane.position.set(0, 1.5, 0.1);
        mesh.add(imagePlane);

        // Add glass protection
        const glassMaterial = this.materials.glassMat.clone();
        const glassPlane = new THREE.Mesh(planeGeometry, glassMaterial);
        glassPlane.position.set(0, 1.5, 0.15);
        mesh.add(glassPlane);
    }

    /**
     * Check if exhibit should have particle effects
     */
    shouldHaveParticles(exhibitName) {
        const particleExhibits = [
            "Genesis Block",
            "Zukunft?",
            "Lightning Network Beta",
            "US Spot ETFs"
        ];

        return particleExhibits.includes(exhibitName);
    }

    /**
     * Add information display elements
     */
    addInformationDisplay(exhibit) {
        const { mesh, eventData } = exhibit;

        // Create information pedestal
        const pedestalGeometry = new THREE.CylinderGeometry(0.8, 1, 0.3, 8);
        const pedestalMaterial = new THREE.MeshStandardMaterial({
            color: 0x2c3e50,
            metalness: 0.6,
            roughness: 0.3
        });
        const pedestal = new THREE.Mesh(pedestalGeometry, pedestalMaterial);
        pedestal.position.set(0, -0.5, 0);
        pedestal.castShadow = true;
        pedestal.receiveShadow = true;
        mesh.add(pedestal);

        // Add category indicator
        if (eventData.category) {
            this.addCategoryIndicator(mesh, eventData.category);
        }
    }

    /**
     * Add category indicator
     */
    addCategoryIndicator(mesh, category) {
        const indicatorGeometry = new THREE.RingGeometry(0.9, 1.1, 8);
        const categoryColors = {
            foundation: 0xf59e0b,
            technology: 0x3b82f6,
            economics: 0x10b981,
            adoption: 0x8b5cf6,
            infrastructure: 0x06b6d4,
            mining: 0xf97316,
            regulation: 0xef4444,
            security: 0xdc2626,
            controversy: 0x7c2d12,
            altcoins: 0x64748b,
            protocol: 0x059669,
            governance: 0x7c3aed
        };

        const indicatorMaterial = new THREE.MeshStandardMaterial({
            color: categoryColors[category] || 0xffffff,
            metalness: 0.8,
            roughness: 0.2,
            emissive: categoryColors[category] || 0x000000,
            emissiveIntensity: 0.2
        });

        const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
        indicator.position.set(0, -0.35, 0);
        indicator.rotation.x = -Math.PI / 2;
        mesh.add(indicator);
    }

    /**
     * Add interactive elements
     */
    addInteractiveElements(exhibit) {
        // Add hover detection zones
        const { mesh } = exhibit;

        // Create invisible interaction sphere
        const interactionGeometry = new THREE.SphereGeometry(3, 8, 6);
        const interactionMaterial = new THREE.MeshBasicMaterial({
            transparent: true,
            opacity: 0,
            visible: false
        });

        const interactionZone = new THREE.Mesh(interactionGeometry, interactionMaterial);
        interactionZone.userData = {
            type: 'interactionZone',
            exhibit: exhibit
        };

        mesh.add(interactionZone);
        exhibit.interactionZone = interactionZone;
    }

    /**
     * Add image plane for special exhibits
     */
    addImagePlane(exhibitGroup, imageUrl) {
        const planeGeometry = new THREE.PlaneGeometry(3, 2.25);
        const imageMaterial = new THREE.MeshStandardMaterial({
            map: this.engine.textureLoader.load(
                imageUrl,
                () => {}, // onLoad
                undefined, // onProgress
                (err) => console.error('Failed to load exhibit image:', err)
            ),
            side: THREE.DoubleSide,
            metalness: 0.1,
            roughness: 0.8
        });
        
        const imagePlane = new THREE.Mesh(planeGeometry, imageMaterial);
        imagePlane.position.set(0, 1.2, 0);
        exhibitGroup.add(imagePlane);
    }

    /**
     * Legacy method - now handled by createAdvancedLighting
     */
    addExhibitLighting(exhibitGroup, isSpecial = false) {
        // This method is now deprecated in favor of createAdvancedLighting
        // Keeping for compatibility
        console.warn('addExhibitLighting is deprecated, use createAdvancedLighting instead');
    }

    /**
     * Check if an exhibit should be animated
     */
    shouldAnimate(exhibitName) {
        const animatedExhibits = [
            "Genesis Block",
            "Zukunft?",
            "Lightning Network Beta",
            "Bitcoin Ordinals"
        ];
        
        return animatedExhibits.includes(exhibitName);
    }

    /**
     * Update exhibit interactions and animations
     */
    update(delta) {
        this.updateExhibitInteractions();
        this.updateAnimations(delta);
    }

    /**
     * Update exhibit interactions (proximity detection)
     */
    updateExhibitInteractions() {
        if (!this.engine.controls.isLocked) return;
        
        const camObject = this.engine.controls.getObject();
        let exhibitFound = false;
        
        for (const exhibit of this.exhibits) {
            if (exhibit.isLabel) continue;
            
            const distance = camObject.position.distanceTo(exhibit.mesh.position);
            
            if (distance < this.interactionDistance) {
                this.showExhibitInfo(exhibit);
                exhibitFound = true;
                break;
            }
        }
        
        if (!exhibitFound) {
            this.hideExhibitInfo();
        }
    }

    /**
     * Show exhibit information
     */
    showExhibitInfo(exhibit) {
        if (this.currentExhibitInfo && this.currentExhibitInfo.id !== exhibit.infoId) {
            this.currentExhibitInfo.style.display = 'none';
        }
        
        this.currentExhibitInfo = document.getElementById(exhibit.infoId);
        if (this.currentExhibitInfo) {
            this.currentExhibitInfo.style.display = 'block';
            
            // Add special styling for special exhibits
            if (exhibit.isSpecial) {
                this.currentExhibitInfo.classList.add('special');
            }
        }
    }

    /**
     * Hide exhibit information
     */
    hideExhibitInfo() {
        if (this.currentExhibitInfo) {
            this.currentExhibitInfo.style.display = 'none';
            this.currentExhibitInfo.classList.remove('special');
            this.currentExhibitInfo = null;
        }
    }

    /**
     * Update exhibit animations with enhanced effects
     */
    updateAnimations(delta) {
        const time = performance.now() * 0.001;

        this.engine.animatedObjects.forEach(exhibit => {
            const mainMesh = exhibit.mainMesh;

            switch (exhibit.name) {
                case "Genesis Block":
                    // Majestic slow rotation with slight bobbing
                    mainMesh.rotation.y += 0.008;
                    mainMesh.position.y = Math.sin(time * 0.5) * 0.1 + (mainMesh.geometry.boundingBox.max.y - mainMesh.geometry.boundingBox.min.y) / 2;

                    // Pulse the emissive intensity
                    if (mainMesh.material.emissive) {
                        mainMesh.material.emissiveIntensity = 0.1 + Math.sin(time * 2) * 0.05;
                    }
                    break;

                case "Zukunft?":
                    // Complex multi-axis rotation with scaling
                    mainMesh.rotation.x += 0.003;
                    mainMesh.rotation.y += 0.004;
                    mainMesh.rotation.z += 0.002;

                    const scale = 1 + Math.sin(time * 1.5) * 0.1;
                    mainMesh.scale.setScalar(scale);

                    // Animate opacity for futuristic effect
                    if (mainMesh.material.transparent) {
                        mainMesh.material.opacity = 0.8 + Math.sin(time * 3) * 0.2;
                    }
                    break;

                case "Lightning Network Beta":
                    // Electric-like rotation with sparks
                    mainMesh.rotation.z += 0.005 + Math.sin(time * 10) * 0.002;
                    mainMesh.rotation.y += 0.001;

                    // Animate emissive for lightning effect
                    if (mainMesh.material.emissive) {
                        mainMesh.material.emissiveIntensity = 0.3 + Math.sin(time * 8) * 0.2;
                    }
                    break;

                case "Bitcoin Ordinals":
                    // Orbital motion
                    mainMesh.rotation.x += 0.002;
                    mainMesh.rotation.y += 0.003;

                    // Create orbital motion around center
                    const orbitRadius = 0.5;
                    mainMesh.position.x = Math.cos(time * 0.8) * orbitRadius;
                    mainMesh.position.z = Math.sin(time * 0.8) * orbitRadius;
                    break;

                case "US Spot ETFs":
                    // Triumphant rising motion
                    mainMesh.position.y = Math.sin(time * 0.7) * 0.2 + (mainMesh.geometry.boundingBox.max.y - mainMesh.geometry.boundingBox.min.y) / 2;
                    mainMesh.rotation.y += 0.005;

                    // Golden glow effect
                    if (mainMesh.material.emissive) {
                        mainMesh.material.emissiveIntensity = 0.15 + Math.sin(time * 1.5) * 0.1;
                    }
                    break;
            }

            // Update particle systems
            if (exhibit.particleSystem) {
                this.updateParticleSystem(exhibit.particleSystem, delta, time);
            }
        });
    }

    /**
     * Update particle system animations
     */
    updateParticleSystem(particleSystem, delta, time) {
        const positions = particleSystem.geometry.attributes.position.array;
        const colors = particleSystem.geometry.attributes.color.array;

        for (let i = 0; i < positions.length; i += 3) {
            // Animate particle positions
            positions[i] += Math.sin(time + i) * 0.01;
            positions[i + 1] += Math.cos(time + i) * 0.01;
            positions[i + 2] += Math.sin(time * 0.5 + i) * 0.01;

            // Animate particle colors
            colors[i] = Math.sin(time + i) * 0.5 + 0.5;
            colors[i + 1] = Math.cos(time + i) * 0.5 + 0.5;
            colors[i + 2] = Math.sin(time * 2 + i) * 0.5 + 0.5;
        }

        particleSystem.geometry.attributes.position.needsUpdate = true;
        particleSystem.geometry.attributes.color.needsUpdate = true;

        // Rotate the entire particle system
        particleSystem.rotation.y += delta * 0.5;
    }

    /**
     * Get exhibit by name
     */
    getExhibitByName(name) {
        return this.exhibits.find(exhibit => exhibit.name === name);
    }

    /**
     * Get exhibits by category
     */
    getExhibitsByCategory(category) {
        return this.exhibits.filter(exhibit => 
            exhibit.eventData && exhibit.eventData.category === category
        );
    }
}
