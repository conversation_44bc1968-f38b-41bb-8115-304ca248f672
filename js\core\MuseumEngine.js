/**
 * MuseumEngine - Core 3D engine for the Bitcoin Museum
 * Handles Three.js scene management, rendering, and core functionality
 */

import * as THREE from 'three';
import { PointerLockControls } from 'three/addons/controls/PointerLockControls.js';
import { FontLoader } from 'three/addons/loaders/FontLoader.js';
import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

export class MuseumEngine {
    constructor() {
        // Core Three.js components
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.font = null;
        this.textureLoader = null;

        // Engine state
        this.isInitialized = false;
        this.isRunning = false;
        this.prevTime = 0;

        // Physics and movement
        this.velocity = new THREE.Vector3();
        this.playerHeight = 1.8;
        this.playerBaseSpeed = 7.0;
        this.playerSprintSpeed = 12.0;
        this.jumpVelocity = 9.0;
        this.gravity = 28.0;

        // Movement state
        this.moveForward = false;
        this.moveBackward = false;
        this.moveLeft = false;
        this.moveRight = false;
        this.isSprinting = false;
        this.canJump = true;
        this.isJumping = false;

        // Collections
        this.exhibits = [];
        this.collidableObjects = [];
        this.animatedObjects = [];

        // Museum dimensions
        this.museumWidth = 80;
        this.totalMuseumLengthZ = 300;
        this.wallHeight = 8;

        // Event handlers
        this.onKeyDown = this.onKeyDown.bind(this);
        this.onKeyUp = this.onKeyUp.bind(this);
        this.onWindowResize = this.onWindowResize.bind(this);
        this.animate = this.animate.bind(this);

        // Callbacks
        this.onLoadProgress = null;
        this.onLoadComplete = null;
        this.onError = null;
    }

    /**
     * Initialize the 3D engine
     */
    async initialize(container) {
        try {
            this.updateLoadingStatus('Initializing 3D Engine...');
            
            // Create scene
            this.scene = new THREE.Scene();
            this.scene.background = new THREE.Color(0x0a0e14);

            // Create camera
            this.camera = new THREE.PerspectiveCamera(
                75,
                window.innerWidth / window.innerHeight,
                0.1,
                1000
            );

            // Create renderer
            this.renderer = new THREE.WebGLRenderer({ 
                antialias: true,
                powerPreference: "high-performance"
            });
            this.renderer.setSize(window.innerWidth, window.innerHeight);
            this.renderer.shadowMap.enabled = true;
            this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            this.renderer.outputColorSpace = THREE.SRGBColorSpace;
            this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
            this.renderer.toneMappingExposure = 1.2;

            container.appendChild(this.renderer.domElement);

            // Initialize texture loader
            this.textureLoader = new THREE.TextureLoader();

            // Load font
            await this.loadFont();

            // Setup controls
            this.setupControls();

            // Setup lighting
            this.setupLighting();

            // Add event listeners
            this.addEventListeners();

            this.isInitialized = true;
            this.updateLoadingStatus('3D Engine initialized successfully');

            if (this.onLoadComplete) {
                this.onLoadComplete();
            }

        } catch (error) {
            console.error('Failed to initialize MuseumEngine:', error);
            if (this.onError) {
                this.onError(error);
            }
            throw error;
        }
    }

    /**
     * Load the font for 3D text
     */
    async loadFont() {
        return new Promise((resolve, reject) => {
            this.updateLoadingStatus('Loading font...');
            
            const fontLoader = new FontLoader();
            fontLoader.load(
                'https://cdn.jsdelivr.net/npm/three@0.160.0/examples/fonts/helvetiker_regular.typeface.json',
                (loadedFont) => {
                    this.font = loadedFont;
                    console.log('Font loaded successfully');
                    resolve();
                },
                (progress) => {
                    // Progress callback
                },
                (error) => {
                    console.warn('Font loading failed, continuing without 3D text:', error);
                    resolve(); // Continue without font
                }
            );
        });
    }

    /**
     * Setup camera controls
     */
    setupControls() {
        this.controls = new PointerLockControls(this.camera, this.renderer.domElement);
        this.scene.add(this.controls.getObject());

        // Set initial camera position
        this.camera.position.set(0, this.playerHeight, (this.totalMuseumLengthZ / 2) - 25);
    }

    /**
     * Setup scene lighting
     */
    setupLighting() {
        // Ambient light for general illumination
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Main directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 2.5);
        directionalLight.position.set(this.museumWidth / 3, this.wallHeight * 6, 0);
        directionalLight.castShadow = true;
        
        // Configure shadow properties
        directionalLight.shadow.mapSize.width = 4096;
        directionalLight.shadow.mapSize.height = 4096;
        directionalLight.shadow.camera.near = 5;
        directionalLight.shadow.camera.far = this.totalMuseumLengthZ * 1.2;
        directionalLight.shadow.camera.left = -this.museumWidth / 1.5;
        directionalLight.shadow.camera.right = this.museumWidth / 1.5;
        directionalLight.shadow.camera.top = this.totalMuseumLengthZ / 1.5;
        directionalLight.shadow.camera.bottom = -this.totalMuseumLengthZ / 1.5;
        
        this.scene.add(directionalLight);

        // Add fog for depth
        this.scene.fog = new THREE.Fog(
            0x0a0e14,
            this.totalMuseumLengthZ * 0.3,
            this.totalMuseumLengthZ * 0.8
        );
    }

    /**
     * Add event listeners
     */
    addEventListeners() {
        document.addEventListener('keydown', this.onKeyDown);
        document.addEventListener('keyup', this.onKeyUp);
        window.addEventListener('resize', this.onWindowResize);
    }

    /**
     * Remove event listeners
     */
    removeEventListeners() {
        document.removeEventListener('keydown', this.onKeyDown);
        document.removeEventListener('keyup', this.onKeyUp);
        window.removeEventListener('resize', this.onWindowResize);
    }

    /**
     * Handle keydown events
     */
    onKeyDown(event) {
        if (!this.controls || !this.controls.isLocked) return;

        switch (event.code) {
            case 'ArrowUp':
            case 'KeyW':
                this.moveForward = true;
                break;
            case 'ArrowLeft':
            case 'KeyA':
                this.moveLeft = true;
                break;
            case 'ArrowDown':
            case 'KeyS':
                this.moveBackward = true;
                break;
            case 'ArrowRight':
            case 'KeyD':
                this.moveRight = true;
                break;
            case 'ShiftLeft':
            case 'ShiftRight':
                this.isSprinting = true;
                break;
            case 'Space':
                if (this.canJump) {
                    this.isJumping = true;
                }
                break;
        }
    }

    /**
     * Handle keyup events
     */
    onKeyUp(event) {
        switch (event.code) {
            case 'ArrowUp':
            case 'KeyW':
                this.moveForward = false;
                break;
            case 'ArrowLeft':
            case 'KeyA':
                this.moveLeft = false;
                break;
            case 'ArrowDown':
            case 'KeyS':
                this.moveBackward = false;
                break;
            case 'ArrowRight':
            case 'KeyD':
                this.moveRight = false;
                break;
            case 'ShiftLeft':
            case 'ShiftRight':
                this.isSprinting = false;
                break;
        }
    }

    /**
     * Handle window resize
     */
    onWindowResize() {
        if (!this.camera || !this.renderer) return;

        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    /**
     * Start the animation loop
     */
    start() {
        if (!this.isInitialized) {
            throw new Error('MuseumEngine must be initialized before starting');
        }

        this.isRunning = true;
        this.prevTime = performance.now();
        this.animate();
    }

    /**
     * Stop the animation loop
     */
    stop() {
        this.isRunning = false;
    }

    /**
     * Main animation loop
     */
    animate() {
        if (!this.isRunning) return;

        requestAnimationFrame(this.animate);

        const time = performance.now();
        const delta = (time - this.prevTime) / 1000;
        this.prevTime = time;

        this.updateMovement(delta);
        this.updateAnimations(delta);
        this.render();
    }

    /**
     * Update player movement and physics
     */
    updateMovement(delta) {
        if (!this.controls.isLocked) return;

        const camObject = this.controls.getObject();
        const actualSpeed = this.isSprinting ? this.playerSprintSpeed : this.playerBaseSpeed;
        const moveDelta = actualSpeed * delta;

        // Apply movement
        if (this.moveForward) this.controls.moveForward(moveDelta);
        if (this.moveBackward) this.controls.moveForward(-moveDelta);
        if (this.moveLeft) this.controls.moveRight(-moveDelta);
        if (this.moveRight) this.controls.moveRight(moveDelta);

        // Handle jumping
        if (this.isJumping) {
            this.velocity.y = this.jumpVelocity;
            this.isJumping = false;
            this.canJump = false;
        }

        // Apply gravity
        camObject.position.y += this.velocity.y * delta;
        this.velocity.y -= this.gravity * delta;

        // Collision detection and boundary constraints will be handled by MuseumLayout
    }

    /**
     * Update animated objects
     */
    updateAnimations(delta) {
        // This will be implemented by specific exhibit managers
    }

    /**
     * Render the scene
     */
    render() {
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    /**
     * Update loading status
     */
    updateLoadingStatus(message) {
        if (this.onLoadProgress) {
            this.onLoadProgress(message);
        }
        console.log('MuseumEngine:', message);
    }

    /**
     * Cleanup resources
     */
    dispose() {
        this.stop();
        this.removeEventListeners();

        if (this.renderer) {
            this.renderer.dispose();
        }

        // Clear collections
        this.exhibits = [];
        this.collidableObjects = [];
        this.animatedObjects = [];
    }
}
