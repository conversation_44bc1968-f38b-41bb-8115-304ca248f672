/**
 * MuseumLayout - Manages the physical structure and layout of the museum
 * Handles room creation, walls, floors, and spatial organization
 */

import * as THREE from 'three';

export class MuseumLayout {
    constructor(engine) {
        this.engine = engine;
        this.rooms = new Map();
        this.walls = [];
        this.floors = [];
        this.ceilings = [];
        this.doors = [];
        
        // Layout configuration
        this.config = {
            wallHeight: 8,
            wallThickness: 1,
            roomPadding: 5,
            doorWidth: 3,
            doorHeight: 2.5
        };
    }

    /**
     * Create the complete museum layout
     */
    createLayout() {
        this.createMainStructure();
        this.createRooms();
        this.createConnections();
        this.addArchitecturalDetails();
    }

    /**
     * Create the main museum structure (outer walls, main floor, ceiling)
     */
    createMainStructure() {
        const { museumWidth, totalMuseumLengthZ, wallHeight } = this.engine;
        const { wallThickness } = this.config;

        // Materials
        const wallMaterial = new THREE.MeshStandardMaterial({
            color: 0x1a202c,
            roughness: 0.8,
            metalness: 0.2
        });

        const floorMaterial = new THREE.MeshStandardMaterial({
            color: 0x2d3748,
            roughness: 0.7
        });

        const ceilingMaterial = new THREE.MeshStandardMaterial({
            color: 0x374151,
            roughness: 0.9
        });

        // Main floor
        const floorGeometry = new THREE.BoxGeometry(
            museumWidth,
            wallThickness,
            totalMuseumLengthZ
        );
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.position.y = -wallThickness / 2;
        floor.receiveShadow = true;
        floor.userData = { type: 'floor', isCollidable: true };
        
        this.engine.scene.add(floor);
        this.engine.collidableObjects.push(floor);
        this.floors.push(floor);

        // Main ceiling
        const ceiling = new THREE.Mesh(floorGeometry, ceilingMaterial);
        ceiling.position.y = wallHeight + wallThickness / 2;
        ceiling.receiveShadow = true;
        ceiling.userData = { type: 'ceiling' };
        
        this.engine.scene.add(ceiling);
        this.ceilings.push(ceiling);

        // Outer walls
        this.createOuterWalls(wallMaterial);
    }

    /**
     * Create outer perimeter walls
     */
    createOuterWalls(wallMaterial) {
        const { museumWidth, totalMuseumLengthZ, wallHeight } = this.engine;
        const { wallThickness } = this.config;

        const wallConfigs = [
            // Left wall
            {
                geometry: new THREE.BoxGeometry(wallThickness, wallHeight, totalMuseumLengthZ),
                position: new THREE.Vector3(-museumWidth / 2 - wallThickness / 2, wallHeight / 2, 0)
            },
            // Right wall
            {
                geometry: new THREE.BoxGeometry(wallThickness, wallHeight, totalMuseumLengthZ),
                position: new THREE.Vector3(museumWidth / 2 + wallThickness / 2, wallHeight / 2, 0)
            },
            // Front wall
            {
                geometry: new THREE.BoxGeometry(museumWidth + wallThickness * 2, wallHeight, wallThickness),
                position: new THREE.Vector3(0, wallHeight / 2, totalMuseumLengthZ / 2 + wallThickness / 2)
            },
            // Back wall
            {
                geometry: new THREE.BoxGeometry(museumWidth + wallThickness * 2, wallHeight, wallThickness),
                position: new THREE.Vector3(0, wallHeight / 2, -totalMuseumLengthZ / 2 - wallThickness / 2)
            }
        ];

        wallConfigs.forEach((config, index) => {
            const wall = new THREE.Mesh(config.geometry, wallMaterial);
            wall.position.copy(config.position);
            wall.castShadow = true;
            wall.receiveShadow = true;
            wall.userData = { type: 'wall', isCollidable: true, wallIndex: index };
            
            this.engine.scene.add(wall);
            this.engine.collidableObjects.push(wall);
            this.walls.push(wall);
        });
    }

    /**
     * Create individual rooms for different eras
     */
    createRooms() {
        const roomConfigs = this.generateRoomConfigurations();
        
        roomConfigs.forEach((config, index) => {
            const room = this.createRoom(config);
            this.rooms.set(config.id, room);
        });
    }

    /**
     * Generate room configurations based on Bitcoin timeline
     */
    generateRoomConfigurations() {
        const { totalMuseumLengthZ, museumWidth } = this.engine;
        const configs = [];

        // Define era themes and their characteristics
        const eras = [
            {
                name: "Genesis Era",
                years: [2008, 2009],
                theme: "foundation",
                color: 0xf59e0b,
                description: "The birth of Bitcoin and blockchain technology"
            },
            {
                name: "Early Adoption",
                years: [2010, 2011],
                theme: "adoption",
                color: 0x3b82f6,
                description: "First real-world usage and early community"
            },
            {
                name: "Infrastructure Building",
                years: [2012, 2013],
                theme: "infrastructure",
                color: 0x10b981,
                description: "Exchanges, services, and first price milestones"
            },
            {
                name: "Growing Pains",
                years: [2014, 2015],
                theme: "challenges",
                color: 0xef4444,
                description: "Major setbacks and regulatory challenges"
            },
            {
                name: "Technical Evolution",
                years: [2016, 2017],
                theme: "technology",
                color: 0x8b5cf6,
                description: "Protocol improvements and mainstream attention"
            },
            {
                name: "Institutional Interest",
                years: [2018, 2019, 2020],
                theme: "institutional",
                color: 0x06b6d4,
                description: "Corporate adoption and institutional investment"
            },
            {
                name: "Global Recognition",
                years: [2021, 2022],
                theme: "global",
                color: 0xf97316,
                description: "Nation-state adoption and mainstream integration"
            },
            {
                name: "Modern Era",
                years: [2023, 2024, 2025],
                theme: "modern",
                color: 0x84cc16,
                description: "ETFs, regulation, and the future of Bitcoin"
            }
        ];

        // Create entrance hall
        configs.push({
            id: "entrance_hall",
            name: "Museum Entrance",
            position: { x: 0, z: totalMuseumLengthZ / 2 - 15 },
            size: { width: museumWidth * 0.8, depth: 25 },
            type: "entrance",
            theme: "welcome",
            color: 0x1a202c,
            description: "Welcome to the Bitcoin Museum"
        });

        // Create era galleries
        const galleryDepth = 60;
        const gallerySpacing = 10;
        let currentZ = totalMuseumLengthZ / 2 - 45; // Start after entrance

        eras.forEach((era, index) => {
            // Main gallery for the era
            const mainGallery = {
                id: `era_${index}_main`,
                name: `${era.name} Gallery`,
                position: { x: 0, z: currentZ },
                size: { width: museumWidth * 0.9, depth: galleryDepth },
                type: "gallery",
                theme: era.theme,
                color: era.color,
                yearRange: era.years,
                description: era.description,
                isMainGallery: true
            };
            configs.push(mainGallery);

            // Create sub-sections within each gallery
            const sectionWidth = (museumWidth * 0.9) / 3;
            era.years.forEach((year, yearIndex) => {
                const sectionX = -sectionWidth + (yearIndex * sectionWidth);
                const section = {
                    id: `era_${index}_year_${year}`,
                    name: `${year} Section`,
                    position: { x: sectionX, z: currentZ },
                    size: { width: sectionWidth * 0.8, depth: galleryDepth * 0.8 },
                    type: "section",
                    theme: era.theme,
                    color: era.color,
                    yearRange: [year],
                    parentGallery: mainGallery.id,
                    description: `Events and developments from ${year}`
                };
                configs.push(section);
            });

            // Add transition corridor
            if (index < eras.length - 1) {
                configs.push({
                    id: `transition_${index}`,
                    name: `Transition to ${eras[index + 1].name}`,
                    position: { x: 0, z: currentZ - galleryDepth / 2 - gallerySpacing / 2 },
                    size: { width: museumWidth * 0.6, depth: gallerySpacing },
                    type: "transition",
                    theme: "neutral",
                    color: 0x4a5568,
                    description: `Connecting ${era.name} to ${eras[index + 1].name}`
                });
            }

            currentZ -= (galleryDepth + gallerySpacing);
        });

        // Create special exhibition hall
        configs.push({
            id: "special_exhibition",
            name: "Future of Bitcoin",
            position: { x: 0, z: currentZ },
            size: { width: museumWidth * 0.7, depth: 40 },
            type: "special",
            theme: "future",
            color: 0x8e44ad,
            description: "Interactive displays about Bitcoin's future potential"
        });

        return configs;
    }

    /**
     * Create a single room
     */
    createRoom(config) {
        const room = {
            id: config.id,
            name: config.name,
            position: config.position,
            size: config.size,
            yearRange: config.yearRange,
            type: config.type,
            theme: config.theme,
            color: config.color,
            description: config.description,
            walls: [],
            exhibits: [],
            lighting: [],
            decorations: []
        };

        // Create room structure based on type
        this.createRoomStructure(room);

        // Create room-specific lighting
        this.addRoomLighting(room);

        // Add room markers/labels
        this.addRoomLabels(room);

        // Add thematic decorations
        this.addRoomDecorations(room);

        return room;
    }

    /**
     * Create the physical structure of a room
     */
    createRoomStructure(room) {
        if (room.type === 'entrance' || room.type === 'special') {
            this.createSpecialRoomStructure(room);
        } else if (room.type === 'gallery') {
            this.createGalleryStructure(room);
        } else if (room.type === 'transition') {
            this.createTransitionStructure(room);
        }
    }

    /**
     * Create structure for special rooms (entrance, future hall)
     */
    createSpecialRoomStructure(room) {
        const { position, size, color } = room;

        // Create elevated platform
        const platformMaterial = new THREE.MeshStandardMaterial({
            color: color,
            roughness: 0.3,
            metalness: 0.7
        });

        const platformGeometry = new THREE.BoxGeometry(size.width, 0.2, size.depth);
        const platform = new THREE.Mesh(platformGeometry, platformMaterial);
        platform.position.set(position.x, 0.1, position.z);
        platform.receiveShadow = true;
        platform.userData = { type: 'platform', roomId: room.id };

        this.engine.scene.add(platform);
        room.walls.push(platform);

        // Add decorative pillars for entrance
        if (room.type === 'entrance') {
            this.addEntrancePillars(room);
        }
    }

    /**
     * Create structure for main galleries
     */
    createGalleryStructure(room) {
        const { position, size, color } = room;

        // Create gallery floor with thematic coloring
        const floorMaterial = new THREE.MeshStandardMaterial({
            color: this.adjustColorBrightness(color, -0.3),
            roughness: 0.8,
            metalness: 0.1
        });

        const floorGeometry = new THREE.BoxGeometry(size.width, 0.1, size.depth);
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.position.set(position.x, 0.05, position.z);
        floor.receiveShadow = true;
        floor.userData = { type: 'galleryFloor', roomId: room.id };

        this.engine.scene.add(floor);
        room.walls.push(floor);

        // Add gallery walls if it's a main gallery
        if (room.isMainGallery) {
            this.addGalleryWalls(room);
        }
    }

    /**
     * Create structure for transition areas
     */
    createTransitionStructure(room) {
        const { position, size } = room;

        // Create transition pathway
        const pathMaterial = new THREE.MeshStandardMaterial({
            color: 0x2d3748,
            roughness: 0.6,
            metalness: 0.2
        });

        const pathGeometry = new THREE.BoxGeometry(size.width, 0.05, size.depth);
        const path = new THREE.Mesh(pathGeometry, pathMaterial);
        path.position.set(position.x, 0.025, position.z);
        path.receiveShadow = true;
        path.userData = { type: 'transitionPath', roomId: room.id };

        this.engine.scene.add(path);
        room.walls.push(path);
    }

    /**
     * Add lighting specific to a room
     */
    addRoomLighting(room) {
        const { position, size, color, type, theme } = room;

        // Base lighting intensity based on room type
        const baseIntensity = {
            entrance: 1.5,
            gallery: 1.2,
            section: 1.0,
            transition: 0.6,
            special: 1.8
        }[type] || 1.0;

        // Main room lighting with thematic color
        const roomLight = new THREE.PointLight(color, baseIntensity, Math.max(size.width, size.depth));
        roomLight.position.set(position.x, this.config.wallHeight - 1, position.z);
        roomLight.castShadow = true;
        roomLight.userData = { type: 'roomLight', roomId: room.id };

        this.engine.scene.add(roomLight);
        room.lighting.push(roomLight);

        // Add dramatic lighting for special themes
        if (theme === 'foundation' || theme === 'future') {
            this.addDramaticLighting(room);
        }

        // Add gallery track lighting
        if (type === 'gallery') {
            this.addTrackLighting(room);
        }

        // Add entrance spotlights
        if (type === 'entrance') {
            this.addEntranceLighting(room);
        }
    }

    /**
     * Add dramatic lighting effects
     */
    addDramaticLighting(room) {
        const { position, size, color } = room;

        // Create multiple spotlights for dramatic effect
        const numSpots = 4;
        for (let i = 0; i < numSpots; i++) {
            const angle = (i / numSpots) * Math.PI * 2;
            const radius = Math.min(size.width, size.depth) * 0.3;

            const spotLight = new THREE.SpotLight(color, 2.0, 25, Math.PI / 8, 0.5, 1);
            spotLight.position.set(
                position.x + Math.cos(angle) * radius,
                this.config.wallHeight - 0.5,
                position.z + Math.sin(angle) * radius
            );

            const target = new THREE.Object3D();
            target.position.set(position.x, 1, position.z);
            this.engine.scene.add(target);
            spotLight.target = target;

            spotLight.castShadow = true;
            spotLight.userData = { type: 'dramaticLight', roomId: room.id };

            this.engine.scene.add(spotLight);
            room.lighting.push(spotLight);
        }
    }

    /**
     * Add track lighting for galleries
     */
    addTrackLighting(room) {
        const { position, size } = room;

        // Create track lights along the gallery
        const numTracks = Math.floor(size.depth / 10);
        for (let i = 0; i < numTracks; i++) {
            const trackZ = position.z - size.depth / 2 + (i + 1) * (size.depth / (numTracks + 1));

            // Left track
            const leftTrack = new THREE.SpotLight(0xffffff, 0.8, 15, Math.PI / 6, 0.3, 1);
            leftTrack.position.set(position.x - size.width / 4, this.config.wallHeight - 1, trackZ);
            leftTrack.target.position.set(position.x - size.width / 4, 0, trackZ);
            leftTrack.castShadow = true;

            // Right track
            const rightTrack = new THREE.SpotLight(0xffffff, 0.8, 15, Math.PI / 6, 0.3, 1);
            rightTrack.position.set(position.x + size.width / 4, this.config.wallHeight - 1, trackZ);
            rightTrack.target.position.set(position.x + size.width / 4, 0, trackZ);
            rightTrack.castShadow = true;

            this.engine.scene.add(leftTrack);
            this.engine.scene.add(leftTrack.target);
            this.engine.scene.add(rightTrack);
            this.engine.scene.add(rightTrack.target);

            room.lighting.push(leftTrack, rightTrack);
        }
    }

    /**
     * Add entrance lighting
     */
    addEntranceLighting(room) {
        const { position, size } = room;

        // Welcome spotlight
        const welcomeLight = new THREE.SpotLight(0xf59e0b, 3.0, 30, Math.PI / 4, 0.2, 1);
        welcomeLight.position.set(position.x, this.config.wallHeight + 2, position.z + size.depth / 3);
        welcomeLight.target.position.set(position.x, 0, position.z);
        welcomeLight.castShadow = true;
        welcomeLight.userData = { type: 'welcomeLight', roomId: room.id };

        this.engine.scene.add(welcomeLight);
        this.engine.scene.add(welcomeLight.target);
        room.lighting.push(welcomeLight);
    }

    /**
     * Add room labels and signage
     */
    addRoomLabels(room) {
        if (!this.engine.font) return;

        const { position, name, type, yearRange, description } = room;

        // Create main room title
        this.createRoomTitle(room);

        // Create year labels for galleries
        if (type === 'gallery' && yearRange) {
            this.createYearLabels(room);
        }

        // Create description plaques
        if (description && (type === 'gallery' || type === 'entrance' || type === 'special')) {
            this.createDescriptionPlaque(room);
        }
    }

    /**
     * Create main room title
     */
    createRoomTitle(room) {
        const { position, name, color, type } = room;

        try {
            const titleGeometry = new THREE.TextGeometry(name, {
                font: this.engine.font,
                size: type === 'entrance' ? 2.5 : 1.8,
                height: 0.15,
                curveSegments: 12,
                bevelEnabled: true,
                bevelThickness: 0.03,
                bevelSize: 0.03,
                bevelOffset: 0,
                bevelSegments: 5
            });

            const titleMaterial = new THREE.MeshStandardMaterial({
                color: color,
                metalness: 0.4,
                roughness: 0.3,
                emissive: this.adjustColorBrightness(color, -0.7)
            });

            const titleMesh = new THREE.Mesh(titleGeometry, titleMaterial);

            // Center the text
            titleGeometry.computeBoundingBox();
            const textWidth = titleGeometry.boundingBox.max.x - titleGeometry.boundingBox.min.x;

            const yPosition = type === 'entrance' ? this.config.wallHeight - 1 : this.config.wallHeight - 1.5;

            titleMesh.position.set(
                position.x - textWidth / 2,
                yPosition,
                position.z + (room.size.depth / 3)
            );

            titleMesh.castShadow = true;
            titleMesh.userData = { type: 'roomTitle', roomId: room.id, isLabel: true };

            this.engine.scene.add(titleMesh);
            room.decorations.push(titleMesh);

        } catch (error) {
            console.warn('Failed to create room title:', error);
        }
    }

    /**
     * Create year labels for galleries
     */
    createYearLabels(room) {
        const { position, yearRange, color } = room;

        if (!yearRange || yearRange.length === 0) return;

        yearRange.forEach((year, index) => {
            try {
                const yearGeometry = new THREE.TextGeometry(year.toString(), {
                    font: this.engine.font,
                    size: 1.2,
                    height: 0.1,
                    curveSegments: 12,
                    bevelEnabled: true,
                    bevelThickness: 0.02,
                    bevelSize: 0.02,
                    bevelOffset: 0,
                    bevelSegments: 5
                });

                const yearMaterial = new THREE.MeshStandardMaterial({
                    color: this.adjustColorBrightness(color, 0.2),
                    metalness: 0.3,
                    roughness: 0.4
                });

                const yearMesh = new THREE.Mesh(yearGeometry, yearMaterial);

                // Position year labels
                yearGeometry.computeBoundingBox();
                const textWidth = yearGeometry.boundingBox.max.x - yearGeometry.boundingBox.min.x;

                const xOffset = (index - (yearRange.length - 1) / 2) * (room.size.width / yearRange.length);

                yearMesh.position.set(
                    position.x + xOffset - textWidth / 2,
                    this.config.wallHeight - 2.5,
                    position.z - room.size.depth / 3
                );

                yearMesh.castShadow = true;
                yearMesh.userData = { type: 'yearLabel', roomId: room.id, year: year, isLabel: true };

                this.engine.scene.add(yearMesh);
                room.decorations.push(yearMesh);

            } catch (error) {
                console.warn(`Failed to create year label for ${year}:`, error);
            }
        });
    }

    /**
     * Create description plaque
     */
    createDescriptionPlaque(room) {
        const { position, description, color } = room;

        // Create a simple plaque geometry
        const plaqueGeometry = new THREE.BoxGeometry(6, 2, 0.1);
        const plaqueMaterial = new THREE.MeshStandardMaterial({
            color: this.adjustColorBrightness(color, -0.4),
            metalness: 0.6,
            roughness: 0.2
        });

        const plaque = new THREE.Mesh(plaqueGeometry, plaqueMaterial);
        plaque.position.set(
            position.x,
            2,
            position.z + room.size.depth / 2.5
        );

        plaque.castShadow = true;
        plaque.receiveShadow = true;
        plaque.userData = {
            type: 'descriptionPlaque',
            roomId: room.id,
            description: description,
            isInteractive: true
        };

        this.engine.scene.add(plaque);
        room.decorations.push(plaque);
    }

    /**
     * Create connections between rooms (doorways, corridors)
     */
    createConnections() {
        // Create main corridor connections
        this.createMainCorridor();
        
        // Add transition areas between major eras
        this.createTransitionAreas();
    }

    /**
     * Create the main corridor running through the museum
     */
    createMainCorridor() {
        // The main corridor is the central walkway
        // This is already handled by the open layout, but we could add
        // decorative elements or guidance systems here
    }

    /**
     * Create transition areas between different eras
     */
    createTransitionAreas() {
        // Add decorative elements or information panels
        // between major historical periods
    }

    /**
     * Add architectural details and decorative elements
     */
    addArchitecturalDetails() {
        this.addColumns();
        this.addDecorations();
        this.addWayfinding();
    }

    /**
     * Add decorative columns
     */
    addColumns() {
        const columnMaterial = new THREE.MeshStandardMaterial({
            color: 0x4a5568,
            roughness: 0.6,
            metalness: 0.1
        });

        const columnGeometry = new THREE.CylinderGeometry(0.3, 0.3, this.config.wallHeight, 8);
        
        // Add columns at strategic positions
        const columnPositions = [
            { x: -30, z: 0 },
            { x: 30, z: 0 },
            { x: 0, z: 75 },
            { x: 0, z: -75 }
        ];

        columnPositions.forEach((pos, index) => {
            const column = new THREE.Mesh(columnGeometry, columnMaterial);
            column.position.set(pos.x, this.config.wallHeight / 2, pos.z);
            column.castShadow = true;
            column.receiveShadow = true;
            column.userData = { type: 'column', isCollidable: true };
            
            this.engine.scene.add(column);
            this.engine.collidableObjects.push(column);
        });
    }

    /**
     * Add decorative elements
     */
    addDecorations() {
        // Add decorative elements like banners, artwork, etc.
        // This will be expanded in future iterations
    }

    /**
     * Add wayfinding elements
     */
    addWayfinding() {
        // Add directional signs, maps, etc.
        // This will be expanded in future iterations
    }

    /**
     * Get room by ID
     */
    getRoom(roomId) {
        return this.rooms.get(roomId);
    }

    /**
     * Get all rooms
     */
    getAllRooms() {
        return Array.from(this.rooms.values());
    }

    /**
     * Find room containing a specific position
     */
    findRoomAtPosition(position) {
        for (const room of this.rooms.values()) {
            const { position: roomPos, size } = room;

            if (position.x >= roomPos.x - size.width / 2 &&
                position.x <= roomPos.x + size.width / 2 &&
                position.z >= roomPos.z - size.depth / 2 &&
                position.z <= roomPos.z + size.depth / 2) {
                return room;
            }
        }
        return null;
    }

    /**
     * Add decorative elements to rooms
     */
    addRoomDecorations(room) {
        const { type, theme, position, size } = room;

        switch (type) {
            case 'entrance':
                this.addEntranceDecorations(room);
                break;
            case 'gallery':
                this.addGalleryDecorations(room);
                break;
            case 'special':
                this.addSpecialDecorations(room);
                break;
        }
    }

    /**
     * Add entrance decorations
     */
    addEntranceDecorations(room) {
        // Add welcome banners
        this.addWelcomeBanners(room);

        // Add information kiosks
        this.addInformationKiosks(room);
    }

    /**
     * Add gallery decorations
     */
    addGalleryDecorations(room) {
        // Add thematic wall panels
        this.addWallPanels(room);

        // Add display cases
        this.addDisplayCases(room);
    }

    /**
     * Add special room decorations
     */
    addSpecialDecorations(room) {
        // Add interactive displays
        this.addInteractiveDisplays(room);
    }

    /**
     * Helper function to adjust color brightness
     */
    adjustColorBrightness(color, factor) {
        const r = (color >> 16) & 0xff;
        const g = (color >> 8) & 0xff;
        const b = color & 0xff;

        const newR = Math.max(0, Math.min(255, r + (r * factor)));
        const newG = Math.max(0, Math.min(255, g + (g * factor)));
        const newB = Math.max(0, Math.min(255, b + (b * factor)));

        return (newR << 16) | (newG << 8) | newB;
    }

    /**
     * Add entrance pillars
     */
    addEntrancePillars(room) {
        const { position, size } = room;

        const pillarMaterial = new THREE.MeshStandardMaterial({
            color: 0xf59e0b,
            metalness: 0.8,
            roughness: 0.2
        });

        const pillarGeometry = new THREE.CylinderGeometry(0.5, 0.6, this.config.wallHeight, 8);

        // Add pillars at entrance
        const pillarPositions = [
            { x: position.x - size.width / 3, z: position.z + size.depth / 3 },
            { x: position.x + size.width / 3, z: position.z + size.depth / 3 }
        ];

        pillarPositions.forEach((pos, index) => {
            const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);
            pillar.position.set(pos.x, this.config.wallHeight / 2, pos.z);
            pillar.castShadow = true;
            pillar.receiveShadow = true;
            pillar.userData = { type: 'entrancePillar', roomId: room.id };

            this.engine.scene.add(pillar);
            this.engine.collidableObjects.push(pillar);
            room.decorations.push(pillar);
        });
    }

    /**
     * Add gallery walls
     */
    addGalleryWalls(room) {
        const { position, size, color } = room;

        const wallMaterial = new THREE.MeshStandardMaterial({
            color: this.adjustColorBrightness(color, -0.5),
            roughness: 0.9,
            metalness: 0.1
        });

        // Create partial walls for gallery definition
        const wallHeight = this.config.wallHeight * 0.6;
        const wallThickness = 0.2;

        // Side walls
        const sideWallGeometry = new THREE.BoxGeometry(wallThickness, wallHeight, size.depth * 0.8);

        const leftWall = new THREE.Mesh(sideWallGeometry, wallMaterial);
        leftWall.position.set(position.x - size.width / 2.2, wallHeight / 2, position.z);
        leftWall.castShadow = true;
        leftWall.receiveShadow = true;

        const rightWall = new THREE.Mesh(sideWallGeometry, wallMaterial);
        rightWall.position.set(position.x + size.width / 2.2, wallHeight / 2, position.z);
        rightWall.castShadow = true;
        rightWall.receiveShadow = true;

        this.engine.scene.add(leftWall);
        this.engine.scene.add(rightWall);
        this.engine.collidableObjects.push(leftWall, rightWall);
        room.walls.push(leftWall, rightWall);
    }

    /**
     * Placeholder methods for future decoration implementations
     */
    addWelcomeBanners(room) {
        // Implementation for welcome banners
    }

    addInformationKiosks(room) {
        // Implementation for information kiosks
    }

    addWallPanels(room) {
        // Implementation for wall panels
    }

    addDisplayCases(room) {
        // Implementation for display cases
    }

    addInteractiveDisplays(room) {
        // Implementation for interactive displays
    }
}
