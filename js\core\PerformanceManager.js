/**
 * PerformanceManager - Handles performance optimization and monitoring
 * Implements LOD systems, frustum culling, and performance monitoring
 */

import * as THREE from 'three';

export class PerformanceManager {
    constructor(engine) {
        this.engine = engine;
        
        // Performance monitoring
        this.stats = {
            fps: 0,
            frameTime: 0,
            drawCalls: 0,
            triangles: 0,
            geometries: 0,
            textures: 0,
            memory: 0
        };
        
        // Performance settings
        this.settings = {
            enableLOD: true,
            enableFrustumCulling: true,
            enableOcclusion: false,
            maxDrawDistance: 200,
            lodDistances: [20, 50, 100],
            targetFPS: 60,
            adaptiveQuality: true
        };
        
        // LOD objects
        this.lodObjects = new Map();
        this.culledObjects = new Set();
        
        // Performance monitoring
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fpsHistory = [];
        
        // Adaptive quality
        this.qualityLevel = 1.0; // 0.5 = low, 1.0 = high
        this.lastQualityAdjustment = 0;
        
        this.initializePerformanceMonitoring();
    }

    /**
     * Initialize performance monitoring
     */
    initializePerformanceMonitoring() {
        // Create performance stats display
        this.createStatsDisplay();
        
        // Setup performance monitoring interval
        setInterval(() => {
            this.updatePerformanceStats();
        }, 1000);
    }

    /**
     * Create performance stats display
     */
    createStatsDisplay() {
        const statsPanel = document.createElement('div');
        statsPanel.id = 'performance-stats';
        statsPanel.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            display: none;
        `;
        
        document.body.appendChild(statsPanel);
        this.statsPanel = statsPanel;
        
        // Toggle stats with F3
        document.addEventListener('keydown', (event) => {
            if (event.code === 'F3') {
                event.preventDefault();
                this.toggleStatsDisplay();
            }
        });
    }

    /**
     * Toggle performance stats display
     */
    toggleStatsDisplay() {
        if (this.statsPanel) {
            const isVisible = this.statsPanel.style.display !== 'none';
            this.statsPanel.style.display = isVisible ? 'none' : 'block';
        }
    }

    /**
     * Update performance statistics
     */
    updatePerformanceStats() {
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;
        
        // Calculate FPS
        this.stats.fps = Math.round(1000 / (deltaTime / this.frameCount));
        this.stats.frameTime = Math.round(deltaTime / this.frameCount * 100) / 100;
        
        // Track FPS history for adaptive quality
        this.fpsHistory.push(this.stats.fps);
        if (this.fpsHistory.length > 10) {
            this.fpsHistory.shift();
        }
        
        // Get renderer info
        if (this.engine.renderer) {
            const info = this.engine.renderer.info;
            this.stats.drawCalls = info.render.calls;
            this.stats.triangles = info.render.triangles;
            this.stats.geometries = info.memory.geometries;
            this.stats.textures = info.memory.textures;
        }
        
        // Estimate memory usage
        this.stats.memory = this.estimateMemoryUsage();
        
        // Update display
        this.updateStatsDisplay();
        
        // Adaptive quality adjustment
        if (this.settings.adaptiveQuality) {
            this.adjustQualityBasedOnPerformance();
        }
        
        // Reset counters
        this.frameCount = 0;
        this.lastTime = currentTime;
    }

    /**
     * Update stats display
     */
    updateStatsDisplay() {
        if (!this.statsPanel || this.statsPanel.style.display === 'none') return;
        
        this.statsPanel.innerHTML = `
            <div>FPS: ${this.stats.fps}</div>
            <div>Frame Time: ${this.stats.frameTime}ms</div>
            <div>Draw Calls: ${this.stats.drawCalls}</div>
            <div>Triangles: ${this.stats.triangles}</div>
            <div>Geometries: ${this.stats.geometries}</div>
            <div>Textures: ${this.stats.textures}</div>
            <div>Memory: ${this.stats.memory}MB</div>
            <div>Quality: ${Math.round(this.qualityLevel * 100)}%</div>
            <div>LOD Objects: ${this.lodObjects.size}</div>
            <div>Culled: ${this.culledObjects.size}</div>
        `;
    }

    /**
     * Estimate memory usage
     */
    estimateMemoryUsage() {
        let memoryUsage = 0;
        
        // Estimate based on renderer info
        if (this.engine.renderer) {
            const info = this.engine.renderer.info;
            // Rough estimation: geometries + textures
            memoryUsage = (info.memory.geometries * 0.1) + (info.memory.textures * 2);
        }
        
        return Math.round(memoryUsage);
    }

    /**
     * Adjust quality based on performance
     */
    adjustQualityBasedOnPerformance() {
        const currentTime = performance.now();
        
        // Only adjust every 5 seconds
        if (currentTime - this.lastQualityAdjustment < 5000) return;
        
        const avgFPS = this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length;
        
        if (avgFPS < this.settings.targetFPS * 0.8) {
            // Performance is poor, reduce quality
            this.qualityLevel = Math.max(0.5, this.qualityLevel - 0.1);
            this.applyQualitySettings();
        } else if (avgFPS > this.settings.targetFPS * 1.1 && this.qualityLevel < 1.0) {
            // Performance is good, increase quality
            this.qualityLevel = Math.min(1.0, this.qualityLevel + 0.1);
            this.applyQualitySettings();
        }
        
        this.lastQualityAdjustment = currentTime;
    }

    /**
     * Apply quality settings to renderer
     */
    applyQualitySettings() {
        if (!this.engine.renderer) return;
        
        // Adjust pixel ratio based on quality
        const pixelRatio = Math.min(window.devicePixelRatio, this.qualityLevel * 2);
        this.engine.renderer.setPixelRatio(pixelRatio);
        
        // Adjust shadow map size
        const shadowMapSize = Math.floor(2048 * this.qualityLevel);
        this.engine.scene.traverse((object) => {
            if (object.isLight && object.shadow) {
                object.shadow.mapSize.width = shadowMapSize;
                object.shadow.mapSize.height = shadowMapSize;
                object.shadow.map?.dispose();
                object.shadow.map = null;
            }
        });
        
        console.log(`Quality adjusted to ${Math.round(this.qualityLevel * 100)}%`);
    }

    /**
     * Create LOD system for an object
     */
    createLODSystem(object, distances = null) {
        if (!this.settings.enableLOD) return object;
        
        const lod = new THREE.LOD();
        const lodDistances = distances || this.settings.lodDistances;
        
        // High detail (original)
        lod.addLevel(object, 0);
        
        // Medium detail
        if (lodDistances[0]) {
            const mediumDetail = this.createReducedDetailMesh(object, 0.7);
            lod.addLevel(mediumDetail, lodDistances[0]);
        }
        
        // Low detail
        if (lodDistances[1]) {
            const lowDetail = this.createReducedDetailMesh(object, 0.4);
            lod.addLevel(lowDetail, lodDistances[1]);
        }
        
        // Very low detail (simple shape)
        if (lodDistances[2]) {
            const veryLowDetail = this.createSimplifiedMesh(object);
            lod.addLevel(veryLowDetail, lodDistances[2]);
        }
        
        this.lodObjects.set(lod.uuid, lod);
        return lod;
    }

    /**
     * Create reduced detail mesh
     */
    createReducedDetailMesh(originalMesh, factor) {
        if (!originalMesh.geometry) return originalMesh.clone();
        
        // Clone the original mesh
        const reducedMesh = originalMesh.clone();
        
        // Simplify geometry (basic approach)
        if (originalMesh.geometry.isBufferGeometry) {
            const geometry = originalMesh.geometry.clone();
            
            // Reduce vertex count by sampling
            if (geometry.attributes.position) {
                const positions = geometry.attributes.position.array;
                const newPositions = new Float32Array(Math.floor(positions.length * factor));
                
                for (let i = 0; i < newPositions.length; i += 3) {
                    const sourceIndex = Math.floor((i / 3) / factor) * 3;
                    newPositions[i] = positions[sourceIndex];
                    newPositions[i + 1] = positions[sourceIndex + 1];
                    newPositions[i + 2] = positions[sourceIndex + 2];
                }
                
                geometry.setAttribute('position', new THREE.BufferAttribute(newPositions, 3));
            }
            
            reducedMesh.geometry = geometry;
        }
        
        return reducedMesh;
    }

    /**
     * Create simplified mesh (basic shape)
     */
    createSimplifiedMesh(originalMesh) {
        // Create a simple box or sphere to represent the object
        const bbox = new THREE.Box3().setFromObject(originalMesh);
        const size = bbox.getSize(new THREE.Vector3());
        
        let geometry;
        if (size.x > size.y && size.x > size.z) {
            // Wide object - use box
            geometry = new THREE.BoxGeometry(size.x, size.y, size.z);
        } else {
            // Tall or round object - use sphere
            const radius = Math.max(size.x, size.y, size.z) / 2;
            geometry = new THREE.SphereGeometry(radius, 8, 6);
        }
        
        const material = originalMesh.material.clone();
        material.wireframe = true;
        material.opacity = 0.5;
        material.transparent = true;
        
        return new THREE.Mesh(geometry, material);
    }

    /**
     * Perform frustum culling
     */
    performFrustumCulling() {
        if (!this.settings.enableFrustumCulling || !this.engine.camera) return;
        
        const camera = this.engine.camera;
        const frustum = new THREE.Frustum();
        const matrix = new THREE.Matrix4().multiplyMatrices(
            camera.projectionMatrix,
            camera.matrixWorldInverse
        );
        frustum.setFromProjectionMatrix(matrix);
        
        this.culledObjects.clear();
        
        this.engine.scene.traverse((object) => {
            if (object.isMesh && object.visible) {
                // Check if object is in frustum
                const sphere = new THREE.Sphere();
                object.geometry.computeBoundingSphere();
                sphere.copy(object.geometry.boundingSphere);
                sphere.applyMatrix4(object.matrixWorld);
                
                if (!frustum.intersectsSphere(sphere)) {
                    object.visible = false;
                    this.culledObjects.add(object.uuid);
                } else {
                    object.visible = true;
                }
            }
        });
    }

    /**
     * Perform distance culling
     */
    performDistanceCulling() {
        if (!this.engine.camera) return;
        
        const cameraPosition = this.engine.camera.position;
        const maxDistance = this.settings.maxDrawDistance;
        
        this.engine.scene.traverse((object) => {
            if (object.isMesh && object.userData.type !== 'essential') {
                const distance = cameraPosition.distanceTo(object.position);
                
                if (distance > maxDistance) {
                    object.visible = false;
                    this.culledObjects.add(object.uuid);
                } else if (this.culledObjects.has(object.uuid)) {
                    object.visible = true;
                    this.culledObjects.delete(object.uuid);
                }
            }
        });
    }

    /**
     * Update LOD objects
     */
    updateLOD() {
        if (!this.settings.enableLOD || !this.engine.camera) return;
        
        this.lodObjects.forEach((lod) => {
            lod.update(this.engine.camera);
        });
    }

    /**
     * Main update function called each frame
     */
    update() {
        this.frameCount++;
        
        // Perform culling operations
        this.performFrustumCulling();
        this.performDistanceCulling();
        
        // Update LOD
        this.updateLOD();
    }

    /**
     * Get performance recommendations
     */
    getPerformanceRecommendations() {
        const recommendations = [];
        
        if (this.stats.fps < 30) {
            recommendations.push("Consider reducing graphics quality");
            recommendations.push("Close other applications");
        }
        
        if (this.stats.drawCalls > 1000) {
            recommendations.push("Too many draw calls - consider object merging");
        }
        
        if (this.stats.triangles > 500000) {
            recommendations.push("High triangle count - enable LOD system");
        }
        
        if (this.stats.memory > 500) {
            recommendations.push("High memory usage - consider texture optimization");
        }
        
        return recommendations;
    }

    /**
     * Dispose of performance manager resources
     */
    dispose() {
        // Clear LOD objects
        this.lodObjects.clear();
        this.culledObjects.clear();
        
        // Remove stats panel
        if (this.statsPanel && this.statsPanel.parentElement) {
            this.statsPanel.parentElement.removeChild(this.statsPanel);
        }
    }
}
