/**
 * Exhibit Content Data
 * Contains all the detailed information for each exhibit in the museum
 */

export const exhibitContent = {
    "info-2008-whitepaper": {
        title: "Bitcoin Whitepaper (2008)",
        content: `
            <p><strong>October 31, 2008</strong> - <PERSON><PERSON> publishes the Bitcoin whitepaper titled "Bitcoin: A Peer-to-Peer Electronic Cash System".</p>
            <p>This 9-page document outlined the fundamental concepts of Bitcoin:</p>
            <ul>
                <li>Peer-to-peer electronic cash without financial institutions</li>
                <li>Proof-of-work consensus mechanism</li>
                <li>Blockchain technology for transaction verification</li>
                <li>Solution to the double-spending problem</li>
            </ul>
            <p><em>This document laid the foundation for the entire cryptocurrency ecosystem.</em></p>
        `
    },
    
    "info-2008-domain": {
        title: "bitcoin.org Domain Registration (2008)",
        content: `
            <p><strong>August 18, 2008</strong> - The domain bitcoin.org was registered anonymously.</p>
            <p>Key details:</p>
            <ul>
                <li>Registered through anonymousspeech.com</li>
                <li>Used privacy protection services</li>
                <li>Became the primary source for Bitcoin information</li>
                <li>Still active today as a community resource</li>
            </ul>
            <p><em>The domain registration preceded the whitepaper by over two months, showing careful planning.</em></p>
        `
    },

    "info-2009-genesis": {
        title: "Genesis Block (2009)",
        content: `
            <p><strong>January 3, 2009</strong> - The first Bitcoin block (Block 0) was mined by <PERSON>shi Nakamoto.</p>
            <p>Historic significance:</p>
            <ul>
                <li>Contains the famous message: "The Times 03/Jan/2009 Chancellor on brink of second bailout for banks"</li>
                <li>References a real newspaper headline, proving the date</li>
                <li>50 BTC reward (unspendable due to code quirk)</li>
                <li>Established the Bitcoin blockchain</li>
            </ul>
            <p><strong>Block Hash:</strong> 000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f</p>
            <p><em>This moment marked the birth of Bitcoin and the beginning of the blockchain era.</em></p>
        `
    },

    "info-2009-transaction": {
        title: "First Bitcoin Transaction (2009)",
        content: `
            <p><strong>January 12, 2009</strong> - The first Bitcoin transaction occurred between Satoshi Nakamoto and Hal Finney.</p>
            <p>Transaction details:</p>
            <ul>
                <li>10 BTC sent from Satoshi to Hal Finney</li>
                <li>Block height: 170</li>
                <li>Proved the system worked as designed</li>
                <li>Hal Finney was the first person besides Satoshi to run Bitcoin</li>
            </ul>
            <p><em>Hal Finney's tweet "Running bitcoin" was one of the first public mentions of Bitcoin.</em></p>
        `
    },

    "info-2009-v01": {
        title: "Bitcoin v0.1 Release (2009)",
        content: `
            <p><strong>January 9, 2009</strong> - Satoshi Nakamoto released the first Bitcoin software (version 0.1).</p>
            <p>Features included:</p>
            <ul>
                <li>Basic wallet functionality</li>
                <li>Mining capability</li>
                <li>Peer-to-peer networking</li>
                <li>Transaction creation and verification</li>
            </ul>
            <p>The software was announced on the Cryptography Mailing List with the message: "Announcing the first release of Bitcoin, a new electronic cash system..."</p>
            <p><em>This release made Bitcoin accessible to anyone willing to download and run the software.</em></p>
        `
    },

    "info-2009-nls": {
        title: "New Liberty Standard Exchange (2009)",
        content: `
            <p><strong>October 5, 2009</strong> - The first Bitcoin exchange rate was established by New Liberty Standard.</p>
            <p>Historic pricing:</p>
            <ul>
                <li>1 USD = 1,309.03 BTC</li>
                <li>Rate calculated based on electricity cost of mining</li>
                <li>First attempt to assign monetary value to Bitcoin</li>
                <li>Enabled the first Bitcoin purchases</li>
            </ul>
            <p><em>This established the precedent for Bitcoin having real-world economic value.</em></p>
        `
    },

    "info-2010-pizza": {
        title: "Bitcoin Pizza Day (2010)",
        content: `
            <p><strong>May 22, 2010</strong> - Laszlo Hanyecz made the first real-world Bitcoin transaction, buying two pizzas for 10,000 BTC.</p>
            <p>Historic transaction:</p>
            <ul>
                <li>Two Papa John's pizzas worth about $25</li>
                <li>10,000 BTC (worth millions today)</li>
                <li>Posted on BitcoinTalk forum</li>
                <li>Proved Bitcoin could be used for real purchases</li>
            </ul>
            <p><strong>May 22nd is now celebrated annually as Bitcoin Pizza Day!</strong></p>
            <p><em>This transaction demonstrated Bitcoin's utility as a medium of exchange.</em></p>
        `
    },

    "info-2010-mtgox": {
        title: "Mt. Gox Exchange Founded (2010)",
        content: `
            <p><strong>July 2010</strong> - Mt. Gox was founded by Jed McCaleb as one of the first Bitcoin exchanges.</p>
            <p>Key milestones:</p>
            <ul>
                <li>Originally a Magic: The Gathering trading card exchange</li>
                <li>Became the dominant Bitcoin exchange</li>
                <li>Handled over 70% of Bitcoin transactions at its peak</li>
                <li>Later sold to Mark Karpelès</li>
            </ul>
            <p><em>Mt. Gox played a crucial role in Bitcoin's early adoption and price discovery.</em></p>
        `
    },

    "info-2010-slushpool": {
        title: "Slush Pool - First Mining Pool (2010)",
        content: `
            <p><strong>November 27, 2010</strong> - Marek Palatinus (slush) created the first Bitcoin mining pool.</p>
            <p>Innovation details:</p>
            <ul>
                <li>Allowed miners to combine computational power</li>
                <li>Shared rewards proportionally</li>
                <li>Reduced variance in mining income</li>
                <li>Still operating today</li>
            </ul>
            <p><em>Mining pools became essential as Bitcoin mining difficulty increased.</em></p>
        `
    },

    "info-2010-value-overflow": {
        title: "Value Overflow Bug (2010)",
        content: `
            <p><strong>August 15, 2010</strong> - A critical bug allowed the creation of 184 billion bitcoins in a single transaction.</p>
            <p>Incident details:</p>
            <ul>
                <li>Exploited integer overflow vulnerability</li>
                <li>Created impossibly large Bitcoin amounts</li>
                <li>Fixed within hours by Satoshi and developers</li>
                <li>Blockchain rolled back to before the incident</li>
            </ul>
            <p><em>This incident demonstrated Bitcoin's resilience and the community's ability to respond to crises.</em></p>
        `
    },

    "info-2010-difficulty": {
        title: "GPU Mining & Difficulty Adjustment (2010)",
        content: `
            <p><strong>2010</strong> - The transition from CPU to GPU mining began, dramatically increasing network security.</p>
            <p>Key developments:</p>
            <ul>
                <li>First GPU mining software released</li>
                <li>Mining difficulty increased significantly</li>
                <li>Network hashrate grew exponentially</li>
                <li>Professionalization of Bitcoin mining</li>
            </ul>
            <p><em>This marked the beginning of the mining arms race that continues today.</em></p>
        `
    },

    "info-2011-silkroad": {
        title: "Silk Road Launch (2011)",
        content: `
            <p><strong>February 2011</strong> - The Silk Road darknet marketplace launched, becoming Bitcoin's first major use case.</p>
            <p>Impact on Bitcoin:</p>
            <ul>
                <li>Demonstrated Bitcoin's censorship resistance</li>
                <li>Increased public awareness (both positive and negative)</li>
                <li>Led to regulatory scrutiny</li>
                <li>Proved Bitcoin's utility for digital commerce</li>
            </ul>
            <p><em>While controversial, Silk Road proved Bitcoin's technical capabilities and sparked important discussions about digital privacy.</em></p>
        `
    },

    "info-2011-litecoin": {
        title: "Litecoin Launch (2011)",
        content: `
            <p><strong>October 7, 2011</strong> - Charlie Lee launched Litecoin, the first major Bitcoin alternative.</p>
            <p>Key features:</p>
            <ul>
                <li>Faster block times (2.5 minutes vs 10 minutes)</li>
                <li>Scrypt proof-of-work algorithm</li>
                <li>4x larger supply cap (84 million LTC)</li>
                <li>"Silver to Bitcoin's gold" positioning</li>
            </ul>
            <p><em>Litecoin paved the way for the broader cryptocurrency ecosystem.</em></p>
        `
    },

    "info-2011-satoshi-last": {
        title: "Satoshi's Final Messages (2011)",
        content: `
            <p><strong>April 2011</strong> - Satoshi Nakamoto sent their final known communications before disappearing.</p>
            <p>Final activities:</p>
            <ul>
                <li>Last forum post on April 23, 2011</li>
                <li>Final email to Gavin Andresen about WikiLeaks</li>
                <li>Gradual reduction in public communications</li>
                <li>Transfer of project leadership to core developers</li>
            </ul>
            <p><em>"I've moved on to other things. It's in good hands with Gavin and everyone."</em></p>
        `
    },

    "info-2011-wikileaks": {
        title: "WikiLeaks Accepts Bitcoin (2011)",
        content: `
            <p><strong>June 2011</strong> - WikiLeaks began accepting Bitcoin donations after traditional payment processors blocked them.</p>
            <p>Significance:</p>
            <ul>
                <li>First major organization to use Bitcoin for censorship resistance</li>
                <li>Demonstrated Bitcoin's utility for controversial causes</li>
                <li>Increased mainstream media attention</li>
                <li>Validated Bitcoin's design principles</li>
            </ul>
            <p><em>This event highlighted Bitcoin's potential as a tool for financial freedom and resistance to censorship.</em></p>
        `
    },

    "info-2012-halving1": {
        title: "First Bitcoin Halving (2012)",
        content: `
            <p><strong>November 28, 2012</strong> - Bitcoin's first halving event reduced the block reward from 50 to 25 BTC.</p>
            <p>Economic impact:</p>
            <ul>
                <li>Reduced new Bitcoin supply by 50%</li>
                <li>Demonstrated Bitcoin's deflationary design</li>
                <li>Price increased from ~$12 to over $1000 in following year</li>
                <li>Established the 4-year halving cycle</li>
            </ul>
            <div class="exhibit-stats">
                <div class="stat-item">
                    <span class="stat-value">50 → 25</span>
                    <span class="stat-label">BTC Reward</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">210,000</span>
                    <span class="stat-label">Blocks</span>
                </div>
            </div>
            <p><em>The first halving proved Bitcoin's monetary policy was working as designed.</em></p>
        `
    },

    "info-2012-foundation": {
        title: "Bitcoin Foundation (2012)",
        content: `
            <p><strong>September 2012</strong> - The Bitcoin Foundation was established to promote Bitcoin development and adoption.</p>
            <p>Founding members:</p>
            <ul>
                <li>Gavin Andresen (Chief Scientist)</li>
                <li>Jon Matonis (Executive Director)</li>
                <li>Peter Vessenes (Chairman)</li>
                <li>Charlie Shrem, Roger Ver, and others</li>
            </ul>
            <p><em>While later controversial, the Foundation played an important early role in Bitcoin's development.</em></p>
        `
    },

    "info-2012-coinbase-founded": {
        title: "Coinbase Founded (2012)",
        content: `
            <p><strong>June 2012</strong> - Brian Armstrong and Fred Ehrsam founded Coinbase to make Bitcoin accessible to mainstream users.</p>
            <p>Early innovations:</p>
            <ul>
                <li>Simple Bitcoin buying and selling</li>
                <li>Bank account integration</li>
                <li>User-friendly interface</li>
                <li>Regulatory compliance focus</li>
            </ul>
            <p><em>Coinbase became the gateway for millions of people to enter the Bitcoin ecosystem.</em></p>
        `
    },

    "info-2012-wordpress": {
        title: "WordPress Accepts Bitcoin (2012)",
        content: `
            <p><strong>November 2012</strong> - WordPress.com became the first major website to accept Bitcoin payments.</p>
            <p>Impact:</p>
            <ul>
                <li>Legitimized Bitcoin for online commerce</li>
                <li>Enabled payments from countries with restricted banking</li>
                <li>Demonstrated Bitcoin's global accessibility</li>
                <li>Inspired other companies to follow</li>
            </ul>
            <p><em>"Bitcoin is a digital currency that enables instant payments to anyone, anywhere in the world."</em> - WordPress announcement</p>
        `
    },

    // Add more exhibit content for remaining years
    "info-2013-price1000": {
        title: "Bitcoin Reaches $1,000 (2013)",
        content: `
            <p><strong>November 2013</strong> - Bitcoin's price reached $1,000 for the first time, marking a major milestone.</p>
            <p>Price journey:</p>
            <ul>
                <li>Started 2013 at around $13</li>
                <li>Reached $1,000 on Mt. Gox exchange</li>
                <li>Massive media attention and public interest</li>
                <li>First major price bubble and crash</li>
            </ul>
            <div class="exhibit-stats">
                <div class="stat-item">
                    <span class="stat-value">$1,000</span>
                    <span class="stat-label">Peak Price</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">7,600%</span>
                    <span class="stat-label">Annual Gain</span>
                </div>
            </div>
            <p><em>This milestone brought Bitcoin into mainstream consciousness for the first time.</em></p>
        `
    },

    "info-2021-elsalvador": {
        title: "El Salvador Bitcoin Law (2021)",
        content: `
            <p><strong>September 7, 2021</strong> - El Salvador became the first country to adopt Bitcoin as legal tender.</p>
            <p>Historic legislation:</p>
            <ul>
                <li>Bitcoin became legal tender alongside the US dollar</li>
                <li>All businesses required to accept Bitcoin</li>
                <li>Government purchased Bitcoin for national reserves</li>
                <li>Chivo wallet launched for citizens</li>
            </ul>
            <div class="exhibit-interactive">
                <h4>Global Impact</h4>
                <p>This decision inspired other nations to consider Bitcoin adoption and demonstrated Bitcoin's potential as a national currency.</p>
            </div>
            <p><em>"This will generate jobs and help provide financial inclusion to thousands of people." - President Nayib Bukele</em></p>
        `
    },

    "info-2024-spot-etf": {
        title: "US Bitcoin Spot ETFs Approved (2024)",
        content: `
            <p><strong>January 10, 2024</strong> - The SEC approved the first Bitcoin spot ETFs in the United States.</p>
            <p>Approved ETFs:</p>
            <ul>
                <li>BlackRock iShares Bitcoin Trust (IBIT)</li>
                <li>Fidelity Wise Origin Bitcoin Fund (FBTC)</li>
                <li>Grayscale Bitcoin Trust (GBTC) - converted</li>
                <li>Multiple other providers</li>
            </ul>
            <div class="exhibit-stats">
                <div class="stat-item">
                    <span class="stat-value">11</span>
                    <span class="stat-label">ETFs Approved</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">$4.6B</span>
                    <span class="stat-label">First Day Volume</span>
                </div>
            </div>
            <p><em>This approval marked Bitcoin's full acceptance by traditional financial institutions.</em></p>
        `
    },

    "info-2025-future": {
        title: "Bitcoin's Future (2025)",
        content: `
            <p><strong>Looking Ahead</strong> - Bitcoin continues to evolve and mature as a global monetary network.</p>
            <p>Current developments:</p>
            <ul>
                <li>Lightning Network scaling solutions</li>
                <li>Institutional adoption acceleration</li>
                <li>Central bank digital currency discussions</li>
                <li>Environmental sustainability improvements</li>
            </ul>
            <div class="exhibit-interactive">
                <h4>What's Next?</h4>
                <p>The future of Bitcoin depends on continued innovation, adoption, and the global community that supports it.</p>
            </div>
            <p class="exhibit-quote">"The future is still being written. Bitcoin's journey is far from over."</p>
        `
    }
};

// Function to inject content into HTML
export function injectExhibitContent() {
    Object.entries(exhibitContent).forEach(([id, data]) => {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = `
                <div class="year-indicator">${extractYear(data.title)}</div>
                <button class="exhibit-close" onclick="this.parentElement.style.display='none'">×</button>
                <h3>${data.title}</h3>
                ${data.content}
            `;
        }
    });
}

// Helper function to extract year from title
function extractYear(title) {
    const match = title.match(/\((\d{4})\)/);
    return match ? match[1] : '';
}

// Function to get exhibit content by ID
export function getExhibitContent(id) {
    return exhibitContent[id] || null;
}

// Function to search exhibit content
export function searchExhibitContent(query) {
    const results = [];
    const searchTerm = query.toLowerCase();

    Object.entries(exhibitContent).forEach(([id, data]) => {
        const title = data.title.toLowerCase();
        const content = data.content.toLowerCase();

        if (title.includes(searchTerm) || content.includes(searchTerm)) {
            results.push({
                id,
                title: data.title,
                relevance: title.includes(searchTerm) ? 2 : 1
            });
        }
    });

    return results.sort((a, b) => b.relevance - a.relevance);
}
