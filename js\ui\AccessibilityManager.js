/**
 * AccessibilityManager - <PERSON>les accessibility features and user interface improvements
 * Provides keyboard navigation, screen reader support, and accessibility enhancements
 */

import * as THREE from 'three';

export class AccessibilityManager {
    constructor(engine, navigationSystem) {
        this.engine = engine;
        this.navigationSystem = navigationSystem;
        
        // Accessibility state
        this.isScreenReaderMode = false;
        this.isHighContrastMode = false;
        this.isReducedMotionMode = false;
        this.fontSize = 1.0;
        this.keyboardNavigationEnabled = true;
        
        // Audio system
        this.audioContext = null;
        this.audioEnabled = true;
        this.narrationEnabled = false;
        this.soundEffectsEnabled = true;
        
        // Keyboard navigation
        this.focusableElements = [];
        this.currentFocusIndex = -1;
        
        this.initializeAccessibility();
    }

    /**
     * Initialize accessibility features
     */
    initializeAccessibility() {
        this.detectAccessibilityPreferences();
        this.setupKeyboardNavigation();
        this.setupAudioSystem();
        this.createAccessibilityControls();
        this.setupEventListeners();
    }

    /**
     * Detect user accessibility preferences
     */
    detectAccessibilityPreferences() {
        // Check for reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            this.isReducedMotionMode = true;
            this.applyReducedMotion();
        }

        // Check for high contrast preference
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            this.isHighContrastMode = true;
            this.applyHighContrast();
        }

        // Check for screen reader
        this.detectScreenReader();
    }

    /**
     * Detect screen reader usage
     */
    detectScreenReader() {
        // Simple screen reader detection
        const testElement = document.createElement('div');
        testElement.setAttribute('aria-hidden', 'true');
        testElement.style.position = 'absolute';
        testElement.style.left = '-10000px';
        testElement.textContent = 'Screen reader test';
        
        document.body.appendChild(testElement);
        
        setTimeout(() => {
            // If element is focused by screen reader, we assume one is active
            if (document.activeElement === testElement) {
                this.isScreenReaderMode = true;
                this.enableScreenReaderMode();
            }
            document.body.removeChild(testElement);
        }, 100);
    }

    /**
     * Setup keyboard navigation
     */
    setupKeyboardNavigation() {
        // Create invisible focus indicators for 3D objects
        this.createFocusIndicators();
        
        // Setup keyboard event handlers
        document.addEventListener('keydown', (event) => {
            this.handleKeyboardNavigation(event);
        });
    }

    /**
     * Create focus indicators for 3D navigation
     */
    createFocusIndicators() {
        // Create focus ring geometry
        const ringGeometry = new THREE.RingGeometry(1.5, 2, 16);
        const ringMaterial = new THREE.MeshBasicMaterial({
            color: 0xffff00,
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide
        });
        
        this.focusRing = new THREE.Mesh(ringGeometry, ringMaterial);
        this.focusRing.visible = false;
        this.focusRing.userData = { type: 'focusIndicator' };
        
        this.engine.scene.add(this.focusRing);
    }

    /**
     * Handle keyboard navigation
     */
    handleKeyboardNavigation(event) {
        if (!this.keyboardNavigationEnabled) return;
        
        switch (event.code) {
            case 'Tab':
                event.preventDefault();
                this.navigateToNextElement(event.shiftKey);
                break;
                
            case 'Enter':
            case 'Space':
                event.preventDefault();
                this.activateCurrentElement();
                break;
                
            case 'Escape':
                this.clearFocus();
                break;
                
            case 'KeyR':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.readCurrentLocation();
                }
                break;
                
            case 'KeyI':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.readCurrentExhibitInfo();
                }
                break;
        }
    }

    /**
     * Navigate to next focusable element
     */
    navigateToNextElement(reverse = false) {
        this.updateFocusableElements();
        
        if (this.focusableElements.length === 0) return;
        
        if (reverse) {
            this.currentFocusIndex--;
            if (this.currentFocusIndex < 0) {
                this.currentFocusIndex = this.focusableElements.length - 1;
            }
        } else {
            this.currentFocusIndex++;
            if (this.currentFocusIndex >= this.focusableElements.length) {
                this.currentFocusIndex = 0;
            }
        }
        
        this.focusElement(this.focusableElements[this.currentFocusIndex]);
    }

    /**
     * Update list of focusable elements
     */
    updateFocusableElements() {
        this.focusableElements = [];
        
        // Add exhibits that are currently visible
        if (this.engine.exhibits) {
            this.engine.exhibits.forEach(exhibit => {
                if (exhibit.mesh.visible && !exhibit.isLabel) {
                    this.focusableElements.push({
                        type: 'exhibit',
                        object: exhibit,
                        position: exhibit.mesh.position
                    });
                }
            });
        }
        
        // Sort by distance from camera
        if (this.engine.camera) {
            const cameraPos = this.engine.camera.position;
            this.focusableElements.sort((a, b) => {
                const distA = cameraPos.distanceTo(a.position);
                const distB = cameraPos.distanceTo(b.position);
                return distA - distB;
            });
        }
    }

    /**
     * Focus on a specific element
     */
    focusElement(element) {
        if (!element) return;
        
        // Position focus ring
        this.focusRing.position.copy(element.position);
        this.focusRing.position.y += 1;
        this.focusRing.visible = true;
        
        // Announce element
        this.announceElement(element);
        
        // Auto-navigate camera if needed
        if (this.isScreenReaderMode) {
            this.autoNavigateToElement(element);
        }
    }

    /**
     * Announce element to screen reader
     */
    announceElement(element) {
        let announcement = '';
        
        switch (element.type) {
            case 'exhibit':
                announcement = `Exhibit: ${element.object.name}. Press Enter to view details.`;
                break;
            default:
                announcement = 'Interactive element';
        }
        
        this.announceToScreenReader(announcement);
        
        // Also provide audio feedback
        if (this.audioEnabled && this.soundEffectsEnabled) {
            this.playFocusSound();
        }
    }

    /**
     * Activate currently focused element
     */
    activateCurrentElement() {
        const element = this.focusableElements[this.currentFocusIndex];
        if (!element) return;
        
        switch (element.type) {
            case 'exhibit':
                this.activateExhibit(element.object);
                break;
        }
    }

    /**
     * Activate an exhibit
     */
    activateExhibit(exhibit) {
        // Show exhibit information
        if (exhibit.infoId) {
            const infoElement = document.getElementById(exhibit.infoId);
            if (infoElement) {
                infoElement.style.display = 'block';
                
                // Announce exhibit content
                if (this.isScreenReaderMode) {
                    this.announceExhibitContent(exhibit);
                }
            }
        }
        
        // Play activation sound
        if (this.audioEnabled && this.soundEffectsEnabled) {
            this.playActivationSound();
        }
    }

    /**
     * Clear current focus
     */
    clearFocus() {
        this.focusRing.visible = false;
        this.currentFocusIndex = -1;
        
        // Hide any open exhibit info
        document.querySelectorAll('.exhibit-info').forEach(info => {
            info.style.display = 'none';
        });
    }

    /**
     * Setup audio system
     */
    setupAudioSystem() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.warn('Audio context not available:', error);
            this.audioEnabled = false;
        }
    }

    /**
     * Play focus sound
     */
    playFocusSound() {
        if (!this.audioContext) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
        
        oscillator.start();
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }

    /**
     * Play activation sound
     */
    playActivationSound() {
        if (!this.audioContext) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(1000, this.audioContext.currentTime);
        oscillator.frequency.setValueAtTime(1200, this.audioContext.currentTime + 0.1);
        gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.2);
        
        oscillator.start();
        oscillator.stop(this.audioContext.currentTime + 0.2);
    }

    /**
     * Announce text to screen reader
     */
    announceToScreenReader(text) {
        // Create or update live region
        let liveRegion = document.getElementById('accessibility-live-region');
        if (!liveRegion) {
            liveRegion = document.createElement('div');
            liveRegion.id = 'accessibility-live-region';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.style.position = 'absolute';
            liveRegion.style.left = '-10000px';
            liveRegion.style.width = '1px';
            liveRegion.style.height = '1px';
            liveRegion.style.overflow = 'hidden';
            document.body.appendChild(liveRegion);
        }
        
        // Clear and set new text
        liveRegion.textContent = '';
        setTimeout(() => {
            liveRegion.textContent = text;
        }, 100);
    }

    /**
     * Read current location
     */
    readCurrentLocation() {
        if (!this.engine.camera) return;
        
        const position = this.engine.camera.position;
        const room = this.navigationSystem?.layout?.findRoomAtPosition(position);
        
        let locationText = `Current location: X ${Math.round(position.x)}, Z ${Math.round(position.z)}`;
        
        if (room) {
            locationText += `. In ${room.name}`;
            if (room.description) {
                locationText += `. ${room.description}`;
            }
        }
        
        this.announceToScreenReader(locationText);
    }

    /**
     * Read current exhibit information
     */
    readCurrentExhibitInfo() {
        const element = this.focusableElements[this.currentFocusIndex];
        if (element && element.type === 'exhibit') {
            this.announceExhibitContent(element.object);
        } else {
            this.announceToScreenReader('No exhibit currently focused');
        }
    }

    /**
     * Announce exhibit content
     */
    announceExhibitContent(exhibit) {
        const infoElement = document.getElementById(exhibit.infoId);
        if (infoElement) {
            const title = infoElement.querySelector('h3')?.textContent || exhibit.name;
            const content = infoElement.textContent.replace(/\s+/g, ' ').trim();
            
            this.announceToScreenReader(`${title}. ${content}`);
        }
    }

    /**
     * Auto-navigate camera to element
     */
    autoNavigateToElement(element) {
        if (!this.engine.controls) return;
        
        const targetPosition = element.position.clone();
        targetPosition.y = this.engine.playerHeight;
        targetPosition.z += 3; // Stand back from object
        
        // Smooth navigation
        this.navigationSystem?.smoothNavigateToPosition(targetPosition);
    }

    /**
     * Create accessibility controls panel
     */
    createAccessibilityControls() {
        const panel = document.createElement('div');
        panel.id = 'accessibility-controls';
        panel.style.cssText = `
            position: fixed;
            top: 50px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 25;
            display: none;
            min-width: 200px;
        `;
        
        panel.innerHTML = `
            <h3>Accessibility Options</h3>
            <label><input type="checkbox" id="high-contrast"> High Contrast</label><br>
            <label><input type="checkbox" id="reduced-motion"> Reduced Motion</label><br>
            <label><input type="checkbox" id="audio-enabled" checked> Audio Feedback</label><br>
            <label><input type="checkbox" id="keyboard-nav" checked> Keyboard Navigation</label><br>
            <label>Font Size: <input type="range" id="font-size" min="0.8" max="1.5" step="0.1" value="1"></label><br>
            <button id="close-accessibility">Close</button>
        `;
        
        document.body.appendChild(panel);
        this.accessibilityPanel = panel;
        
        // Setup event listeners
        this.setupAccessibilityControls();
    }

    /**
     * Setup accessibility control event listeners
     */
    setupAccessibilityControls() {
        const panel = this.accessibilityPanel;
        if (!panel) return;
        
        panel.querySelector('#high-contrast').addEventListener('change', (e) => {
            this.toggleHighContrast(e.target.checked);
        });
        
        panel.querySelector('#reduced-motion').addEventListener('change', (e) => {
            this.toggleReducedMotion(e.target.checked);
        });
        
        panel.querySelector('#audio-enabled').addEventListener('change', (e) => {
            this.audioEnabled = e.target.checked;
        });
        
        panel.querySelector('#keyboard-nav').addEventListener('change', (e) => {
            this.keyboardNavigationEnabled = e.target.checked;
        });
        
        panel.querySelector('#font-size').addEventListener('input', (e) => {
            this.adjustFontSize(parseFloat(e.target.value));
        });
        
        panel.querySelector('#close-accessibility').addEventListener('click', () => {
            panel.style.display = 'none';
        });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Accessibility panel toggle (Ctrl+A)
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.code === 'KeyA') {
                event.preventDefault();
                this.toggleAccessibilityPanel();
            }
        });
    }

    /**
     * Toggle accessibility panel
     */
    toggleAccessibilityPanel() {
        if (!this.accessibilityPanel) return;
        
        const isVisible = this.accessibilityPanel.style.display !== 'none';
        this.accessibilityPanel.style.display = isVisible ? 'none' : 'block';
    }

    /**
     * Apply accessibility settings
     */
    applyReducedMotion() {
        // Disable animations and transitions
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    }

    applyHighContrast() {
        document.body.classList.add('high-contrast');
        
        // Add high contrast styles
        const style = document.createElement('style');
        style.textContent = `
            .high-contrast {
                filter: contrast(150%) brightness(120%);
            }
            .high-contrast .exhibit-info {
                background: #000000 !important;
                color: #ffffff !important;
                border: 2px solid #ffffff !important;
            }
        `;
        document.head.appendChild(style);
    }

    toggleHighContrast(enabled) {
        this.isHighContrastMode = enabled;
        if (enabled) {
            this.applyHighContrast();
        } else {
            document.body.classList.remove('high-contrast');
        }
    }

    toggleReducedMotion(enabled) {
        this.isReducedMotionMode = enabled;
        if (enabled) {
            this.applyReducedMotion();
        }
    }

    adjustFontSize(scale) {
        this.fontSize = scale;
        document.documentElement.style.fontSize = `${scale}rem`;
    }

    enableScreenReaderMode() {
        this.isScreenReaderMode = true;
        this.announceToScreenReader('Screen reader mode enabled. Use Tab to navigate between exhibits, Enter to activate, and Ctrl+R to read current location.');
    }

    /**
     * Update accessibility manager
     */
    update() {
        // Update focus ring animation
        if (this.focusRing && this.focusRing.visible) {
            const time = performance.now() * 0.001;
            this.focusRing.rotation.z = time;
            this.focusRing.material.opacity = 0.6 + Math.sin(time * 3) * 0.2;
        }
    }

    /**
     * Dispose of accessibility manager
     */
    dispose() {
        if (this.accessibilityPanel && this.accessibilityPanel.parentElement) {
            this.accessibilityPanel.parentElement.removeChild(this.accessibilityPanel);
        }
        
        if (this.audioContext) {
            this.audioContext.close();
        }
    }
}
