/**
 * NavigationSystem - Advanced navigation and user interface system
 * Handles guided tours, waypoints, mini-map, and user interface elements
 */

import * as THREE from 'three';

export class NavigationSystem {
    constructor(engine, layout, exhibitManager) {
        this.engine = engine;
        this.layout = layout;
        this.exhibitManager = exhibitManager;
        
        // Navigation state
        this.isGuidedTourActive = false;
        this.currentTourStep = 0;
        this.tourWaypoints = [];
        this.isNavigating = false;
        
        // UI elements
        this.miniMap = null;
        this.navigationUI = null;
        this.progressIndicator = null;
        this.wayfindingArrows = [];
        
        // Tour configuration
        this.tourSpeed = 8.0;
        this.tourStopDuration = 5000; // 5 seconds per stop
        
        this.initializeUI();
        this.createTourWaypoints();
    }

    /**
     * Initialize navigation UI elements
     */
    initializeUI() {
        this.createMiniMap();
        this.createNavigationControls();
        this.createProgressIndicator();
        this.createWayfindingSystem();
        this.setupEventListeners();
    }

    /**
     * Create mini-map display
     */
    createMiniMap() {
        this.miniMap = document.querySelector('.mini-map');
        if (!this.miniMap) return;

        // Create mini-map canvas
        const canvas = document.createElement('canvas');
        canvas.width = 180;
        canvas.height = 135;
        canvas.style.width = '100%';
        canvas.style.height = 'calc(100% - 40px)';
        
        const mapContent = this.miniMap.querySelector('.mini-map-content');
        if (mapContent) {
            mapContent.appendChild(canvas);
        }
        
        this.miniMapCanvas = canvas;
        this.miniMapContext = canvas.getContext('2d');
        
        // Show mini-map
        this.miniMap.style.display = 'block';
        
        this.updateMiniMap();
    }

    /**
     * Create navigation controls
     */
    createNavigationControls() {
        this.navigationUI = document.querySelector('.navigation-ui');
        if (!this.navigationUI) return;

        // Show navigation UI
        this.navigationUI.style.display = 'block';

        // Setup button handlers
        const helpBtn = document.getElementById('nav-help');
        const mapBtn = document.getElementById('nav-map');
        const settingsBtn = document.getElementById('nav-settings');

        if (helpBtn) {
            helpBtn.addEventListener('click', () => this.toggleHelp());
        }
        
        if (mapBtn) {
            mapBtn.addEventListener('click', () => this.toggleMiniMap());
        }
        
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.openSettings());
        }

        // Add tour controls
        this.addTourControls();
    }

    /**
     * Add guided tour controls
     */
    addTourControls() {
        if (!this.navigationUI) return;

        const tourButton = document.createElement('button');
        tourButton.className = 'nav-button';
        tourButton.id = 'nav-tour';
        tourButton.textContent = 'Guided Tour';
        tourButton.addEventListener('click', () => this.toggleGuidedTour());
        
        const waypointButton = document.createElement('button');
        waypointButton.className = 'nav-button';
        waypointButton.id = 'nav-waypoint';
        waypointButton.textContent = 'Next Stop';
        waypointButton.addEventListener('click', () => this.nextWaypoint());
        waypointButton.style.display = 'none';
        
        this.navigationUI.appendChild(tourButton);
        this.navigationUI.appendChild(waypointButton);
        
        this.tourButton = tourButton;
        this.waypointButton = waypointButton;
    }

    /**
     * Create progress indicator
     */
    createProgressIndicator() {
        this.progressIndicator = document.querySelector('.progress-indicator');
        if (!this.progressIndicator) return;

        this.progressText = this.progressIndicator.querySelector('.progress-text');
        this.progressFill = this.progressIndicator.querySelector('.progress-fill');
    }

    /**
     * Create wayfinding system
     */
    createWayfindingSystem() {
        // Create 3D arrows for wayfinding
        this.createWayfindingArrows();
    }

    /**
     * Create 3D wayfinding arrows
     */
    createWayfindingArrows() {
        const arrowGeometry = new THREE.ConeGeometry(0.3, 1.5, 8);
        const arrowMaterial = new THREE.MeshStandardMaterial({
            color: 0xf59e0b,
            emissive: 0x331100,
            emissiveIntensity: 0.3,
            transparent: true,
            opacity: 0.8
        });

        // Create multiple arrows for different directions
        for (let i = 0; i < 5; i++) {
            const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);
            arrow.visible = false;
            arrow.userData = { type: 'wayfindingArrow', index: i };
            
            this.engine.scene.add(arrow);
            this.wayfindingArrows.push(arrow);
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            if (!this.engine.controls.isLocked) return;
            
            switch (event.code) {
                case 'KeyM':
                    this.toggleMiniMap();
                    break;
                case 'KeyT':
                    this.toggleGuidedTour();
                    break;
                case 'KeyN':
                    if (this.isGuidedTourActive) {
                        this.nextWaypoint();
                    }
                    break;
                case 'KeyH':
                    this.toggleHelp();
                    break;
            }
        });
    }

    /**
     * Create tour waypoints based on important exhibits
     */
    createTourWaypoints() {
        const importantExhibits = [
            "Whitepaper 2008",
            "Genesis Block",
            "Bitcoin Pizza",
            "Erstes Halving",
            "Mt. Gox Kollaps",
            "Zweites Halving",
            "Bull Run $20k",
            "Lightning Network Beta",
            "Drittes Halving",
            "El Salvador",
            "FTX Kollaps",
            "US Spot ETFs",
            "Viertes Halving",
            "Zukunft?"
        ];

        this.tourWaypoints = [];
        
        importantExhibits.forEach(exhibitName => {
            const exhibit = this.exhibitManager.getExhibitByName(exhibitName);
            if (exhibit) {
                const position = exhibit.mesh.position.clone();
                position.y = this.engine.playerHeight;
                position.z += 4; // Stand back from exhibit
                
                this.tourWaypoints.push({
                    name: exhibitName,
                    position: position,
                    exhibit: exhibit,
                    description: this.getWaypointDescription(exhibitName)
                });
            }
        });
    }

    /**
     * Get description for waypoint
     */
    getWaypointDescription(exhibitName) {
        const descriptions = {
            "Whitepaper 2008": "The beginning - Satoshi's revolutionary paper",
            "Genesis Block": "The first Bitcoin block ever mined",
            "Bitcoin Pizza": "The first real-world Bitcoin transaction",
            "Erstes Halving": "Bitcoin's first supply reduction event",
            "Mt. Gox Kollaps": "A major crisis that tested Bitcoin's resilience",
            "Zweites Halving": "The second halving event",
            "Bull Run $20k": "Bitcoin's first major price milestone",
            "Lightning Network Beta": "Scaling solution for faster payments",
            "Drittes Halving": "The third halving event",
            "El Salvador": "First country to adopt Bitcoin as legal tender",
            "FTX Kollaps": "Another major crisis and lessons learned",
            "US Spot ETFs": "Institutional adoption milestone",
            "Viertes Halving": "The most recent halving event",
            "Zukunft?": "What lies ahead for Bitcoin"
        };
        
        return descriptions[exhibitName] || "An important moment in Bitcoin history";
    }

    /**
     * Toggle guided tour
     */
    toggleGuidedTour() {
        if (this.isGuidedTourActive) {
            this.stopGuidedTour();
        } else {
            this.startGuidedTour();
        }
    }

    /**
     * Start guided tour
     */
    startGuidedTour() {
        if (this.tourWaypoints.length === 0) {
            console.warn('No tour waypoints available');
            return;
        }

        this.isGuidedTourActive = true;
        this.currentTourStep = 0;
        
        // Update UI
        this.tourButton.textContent = 'Stop Tour';
        this.waypointButton.style.display = 'block';
        
        // Show progress indicator
        if (this.progressIndicator) {
            this.progressIndicator.style.display = 'block';
        }
        
        // Start tour
        this.navigateToWaypoint(0);
        
        console.log('Guided tour started');
    }

    /**
     * Stop guided tour
     */
    stopGuidedTour() {
        this.isGuidedTourActive = false;
        this.isNavigating = false;
        
        // Update UI
        this.tourButton.textContent = 'Guided Tour';
        this.waypointButton.style.display = 'none';
        
        // Hide progress indicator
        if (this.progressIndicator) {
            this.progressIndicator.style.display = 'none';
        }
        
        // Hide wayfinding arrows
        this.hideWayfindingArrows();
        
        console.log('Guided tour stopped');
    }

    /**
     * Navigate to next waypoint
     */
    nextWaypoint() {
        if (!this.isGuidedTourActive) return;
        
        this.currentTourStep++;
        
        if (this.currentTourStep >= this.tourWaypoints.length) {
            // Tour completed
            this.stopGuidedTour();
            this.showTourCompletionMessage();
            return;
        }
        
        this.navigateToWaypoint(this.currentTourStep);
    }

    /**
     * Navigate to specific waypoint
     */
    navigateToWaypoint(index) {
        if (index < 0 || index >= this.tourWaypoints.length) return;
        
        const waypoint = this.tourWaypoints[index];
        this.isNavigating = true;
        
        // Update progress
        this.updateProgress(index + 1, this.tourWaypoints.length, waypoint.name);
        
        // Show wayfinding arrows
        this.showWayfindingArrows(waypoint.position);
        
        // Smoothly move camera to waypoint
        this.smoothNavigateToPosition(waypoint.position, () => {
            this.isNavigating = false;
            this.onWaypointReached(waypoint);
        });
    }

    /**
     * Smooth navigation to position
     */
    smoothNavigateToPosition(targetPosition, onComplete) {
        if (!this.engine.controls) return;
        
        const startPosition = this.engine.controls.getObject().position.clone();
        const distance = startPosition.distanceTo(targetPosition);
        const duration = Math.max(2000, distance * 100); // Adjust speed based on distance
        
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Smooth easing function
            const easeProgress = 1 - Math.pow(1 - progress, 3);
            
            // Interpolate position
            const currentPosition = startPosition.clone().lerp(targetPosition, easeProgress);
            this.engine.controls.getObject().position.copy(currentPosition);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                if (onComplete) onComplete();
            }
        };
        
        requestAnimationFrame(animate);
    }

    /**
     * Show wayfinding arrows pointing to target
     */
    showWayfindingArrows(targetPosition) {
        if (!this.engine.controls) return;
        
        const playerPosition = this.engine.controls.getObject().position;
        const direction = targetPosition.clone().sub(playerPosition).normalize();
        
        // Position arrows in a path towards target
        this.wayfindingArrows.forEach((arrow, index) => {
            const distance = 5 + index * 8;
            const arrowPosition = playerPosition.clone().add(direction.clone().multiplyScalar(distance));
            arrowPosition.y = this.engine.playerHeight + 1;
            
            arrow.position.copy(arrowPosition);
            arrow.lookAt(targetPosition);
            arrow.visible = true;
        });
    }

    /**
     * Hide wayfinding arrows
     */
    hideWayfindingArrows() {
        this.wayfindingArrows.forEach(arrow => {
            arrow.visible = false;
        });
    }

    /**
     * Handle waypoint reached
     */
    onWaypointReached(waypoint) {
        console.log(`Reached waypoint: ${waypoint.name}`);
        
        // Hide arrows
        this.hideWayfindingArrows();
        
        // Show exhibit information
        if (waypoint.exhibit && waypoint.exhibit.infoId) {
            this.exhibitManager.showExhibitInfo(waypoint.exhibit);
        }
        
        // Auto-advance after delay (optional)
        if (this.isGuidedTourActive && this.currentTourStep < this.tourWaypoints.length - 1) {
            setTimeout(() => {
                if (this.isGuidedTourActive && !this.isNavigating) {
                    this.nextWaypoint();
                }
            }, this.tourStopDuration);
        }
    }

    /**
     * Update progress indicator
     */
    updateProgress(current, total, currentLocation) {
        if (!this.progressIndicator) return;
        
        const percentage = (current / total) * 100;
        
        if (this.progressText) {
            this.progressText.textContent = `${current}/${total} - ${currentLocation}`;
        }
        
        if (this.progressFill) {
            this.progressFill.style.width = `${percentage}%`;
        }
    }

    /**
     * Show tour completion message
     */
    showTourCompletionMessage() {
        // Create completion overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(20, 25, 35, 0.95);
            border: 2px solid #f59e0b;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            color: white;
            z-index: 30;
            backdrop-filter: blur(10px);
        `;
        
        overlay.innerHTML = `
            <h2 style="color: #f59e0b; margin-bottom: 15px;">Tour Complete!</h2>
            <p>You've explored the complete history of Bitcoin.</p>
            <p>Feel free to continue exploring on your own.</p>
            <button onclick="this.parentElement.remove()" style="
                margin-top: 20px;
                background: #f59e0b;
                color: #111827;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                cursor: pointer;
                font-weight: bold;
            ">Continue Exploring</button>
        `;
        
        document.body.appendChild(overlay);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (overlay.parentElement) {
                overlay.remove();
            }
        }, 10000);
    }

    /**
     * Update mini-map
     */
    updateMiniMap() {
        if (!this.miniMapCanvas || !this.miniMapContext) return;
        
        const ctx = this.miniMapContext;
        const canvas = this.miniMapCanvas;
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw museum layout
        ctx.fillStyle = '#2d3748';
        ctx.fillRect(10, 10, canvas.width - 20, canvas.height - 20);
        
        // Draw rooms
        this.layout.getAllRooms().forEach(room => {
            const { position, size, color } = room;
            
            // Convert 3D position to 2D map coordinates
            const mapX = ((position.x + this.engine.museumWidth / 2) / this.engine.museumWidth) * (canvas.width - 20) + 10;
            const mapZ = ((position.z + this.engine.totalMuseumLengthZ / 2) / this.engine.totalMuseumLengthZ) * (canvas.height - 20) + 10;
            const mapW = (size.width / this.engine.museumWidth) * (canvas.width - 20);
            const mapH = (size.depth / this.engine.totalMuseumLengthZ) * (canvas.height - 20);
            
            ctx.fillStyle = `#${color.toString(16).padStart(6, '0')}`;
            ctx.globalAlpha = 0.3;
            ctx.fillRect(mapX - mapW / 2, mapZ - mapH / 2, mapW, mapH);
            ctx.globalAlpha = 1.0;
        });
        
        // Draw player position
        if (this.engine.controls) {
            const playerPos = this.engine.controls.getObject().position;
            const playerMapX = ((playerPos.x + this.engine.museumWidth / 2) / this.engine.museumWidth) * (canvas.width - 20) + 10;
            const playerMapZ = ((playerPos.z + this.engine.totalMuseumLengthZ / 2) / this.engine.totalMuseumLengthZ) * (canvas.height - 20) + 10;
            
            ctx.fillStyle = '#f59e0b';
            ctx.beginPath();
            ctx.arc(playerMapX, playerMapZ, 3, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    /**
     * Toggle mini-map visibility
     */
    toggleMiniMap() {
        if (!this.miniMap) return;
        
        const isVisible = this.miniMap.style.display !== 'none';
        this.miniMap.style.display = isVisible ? 'none' : 'block';
        
        if (!isVisible) {
            this.updateMiniMap();
        }
    }

    /**
     * Toggle help display
     */
    toggleHelp() {
        const helpPanel = document.querySelector('.controls-help');
        if (!helpPanel) return;
        
        const isVisible = helpPanel.style.display !== 'none';
        helpPanel.style.display = isVisible ? 'none' : 'block';
    }

    /**
     * Open settings panel
     */
    openSettings() {
        // Implementation for settings panel
        console.log('Settings panel requested');
    }

    /**
     * Update navigation system
     */
    update() {
        // Update mini-map periodically
        if (this.miniMap && this.miniMap.style.display !== 'none') {
            this.updateMiniMap();
        }
        
        // Update wayfinding arrows animation
        this.updateWayfindingArrows();
    }

    /**
     * Update wayfinding arrows animation
     */
    updateWayfindingArrows() {
        const time = performance.now() * 0.001;
        
        this.wayfindingArrows.forEach((arrow, index) => {
            if (arrow.visible) {
                // Bobbing animation
                arrow.position.y = this.engine.playerHeight + 1 + Math.sin(time * 3 + index) * 0.2;
                
                // Pulsing glow
                if (arrow.material.emissiveIntensity !== undefined) {
                    arrow.material.emissiveIntensity = 0.3 + Math.sin(time * 4 + index) * 0.2;
                }
            }
        });
    }
}
