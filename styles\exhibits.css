/* Exhibit Information Panels and Display Styles */

.exhibit-info {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(20, 25, 35, 0.97);
    color: white;
    padding: 25px;
    border-radius: 15px;
    border: 1px solid #4a5568;
    max-width: 90%;
    width: 600px;
    text-align: left;
    font-size: 15px;
    display: none;
    z-index: 10;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(15px);
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.exhibit-info.closing {
    animation: slideOutUp 0.3s ease-in forwards;
}

@keyframes slideOutUp {
    from {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    to {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
}

.exhibit-info h3 {
    font-size: 22px;
    margin-bottom: 15px;
    color: #f59e0b;
    font-weight: bold;
    border-bottom: 2px solid #f59e0b;
    padding-bottom: 8px;
}

.exhibit-info p,
.exhibit-info ul {
    margin-bottom: 12px;
    line-height: 1.7;
    color: #e5e7eb;
}

.exhibit-info ul {
    list-style-position: inside;
    padding-left: 10px;
}

.exhibit-info li {
    margin-bottom: 6px;
    position: relative;
}

.exhibit-info li::marker {
    color: #f59e0b;
}

.exhibit-info img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.exhibit-info strong {
    color: #fbbf24;
    font-weight: 600;
}

.exhibit-info em {
    color: #9ca3af;
    font-size: 0.9em;
    font-style: italic;
}

/* Special exhibit styling */
.exhibit-info.special {
    border: 2px solid #f59e0b;
    box-shadow: 0 0 30px rgba(245, 158, 11, 0.3);
}

.exhibit-info.special h3 {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Year indicators */
.year-indicator {
    position: absolute;
    top: -10px;
    right: 20px;
    background: #f59e0b;
    color: #111827;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

/* Close button for exhibits */
.exhibit-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 200ms ease;
}

.exhibit-close:hover {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

/* Interactive elements within exhibits */
.exhibit-interactive {
    margin: 15px 0;
    padding: 15px;
    background: rgba(55, 65, 81, 0.5);
    border-radius: 8px;
    border-left: 4px solid #f59e0b;
}

.exhibit-interactive h4 {
    color: #fbbf24;
    margin-bottom: 8px;
    font-size: 16px;
}

.exhibit-link {
    color: #60a5fa;
    text-decoration: none;
    border-bottom: 1px dotted #60a5fa;
    transition: all 200ms ease;
}

.exhibit-link:hover {
    color: #93c5fd;
    border-bottom-style: solid;
}

/* Timeline elements */
.timeline-marker {
    display: inline-block;
    background: #374151;
    color: #f59e0b;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 8px;
    vertical-align: middle;
}

/* Quote styling */
.exhibit-quote {
    font-style: italic;
    color: #d1d5db;
    border-left: 3px solid #6b7280;
    padding-left: 15px;
    margin: 15px 0;
    background: rgba(55, 65, 81, 0.3);
    padding: 12px 15px;
    border-radius: 0 8px 8px 0;
}

/* Statistics display */
.exhibit-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin: 15px 0;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background: rgba(55, 65, 81, 0.4);
    border-radius: 8px;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #f59e0b;
}

.stat-label {
    font-size: 12px;
    color: #9ca3af;
    margin-top: 2px;
}

/* Responsive design for exhibits */
@media (max-width: 768px) {
    .exhibit-info {
        width: 95%;
        padding: 20px;
        font-size: 14px;
        top: 10px;
    }
    
    .exhibit-info h3 {
        font-size: 18px;
    }
    
    .exhibit-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .exhibit-info {
        padding: 15px;
        font-size: 13px;
    }
    
    .exhibit-info h3 {
        font-size: 16px;
    }
    
    .exhibit-stats {
        grid-template-columns: 1fr;
    }
}

/* Print styles */
@media print {
    .exhibit-info {
        position: static;
        transform: none;
        background: white;
        color: black;
        box-shadow: none;
        border: 1px solid #ccc;
        page-break-inside: avoid;
    }
    
    .exhibit-info h3 {
        color: #333;
        border-bottom-color: #333;
    }
}
