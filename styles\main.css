/* Main styles for the Bitcoin Museum */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    margin: 0;
    overflow: hidden;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background-color: #0a0e14;
    color: #e5e7eb;
    line-height: 1.6;
}

/* Canvas container */
#canvas-container {
    width: 100vw;
    height: 100vh;
    display: block;
    position: relative;
}

/* Loading and blocker overlay */
#blocker {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
    cursor: pointer;
    z-index: 20;
    backdrop-filter: blur(5px);
}

#instructions-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    max-width: 800px;
}

#instructions-container p {
    margin: 10px 0;
    font-size: 24px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

#instructions-container p.title {
    font-size: 48px;
    font-weight: bold;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
}

#start-museum-btn {
    margin-top: 2rem;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: #111827;
    font-weight: 700;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-size: 1.25rem;
    border: none;
    cursor: pointer;
    transition: all 300ms ease-in-out;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#start-museum-btn:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

#consent-info {
    font-size: 14px;
    color: #cbd5e0;
    max-width: 600px;
    margin-top: 20px;
    line-height: 1.5;
    text-align: center;
    opacity: 0.8;
}

#loading-status {
    font-size: 28px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Responsive design */
@media (max-width: 768px) {
    #instructions-container p.title {
        font-size: 36px;
    }
    
    #instructions-container p {
        font-size: 18px;
    }
    
    #start-museum-btn {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }
    
    #consent-info {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    #instructions-container {
        padding: 15px;
    }
    
    #instructions-container p.title {
        font-size: 28px;
    }
    
    #instructions-container p {
        font-size: 16px;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    #loading-status {
        animation: none;
    }
    
    #start-museum-btn {
        transition: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    body {
        background-color: #000000;
        color: #ffffff;
    }
    
    #blocker {
        background-color: rgba(0, 0, 0, 0.95);
    }
    
    #start-museum-btn {
        background: #ffffff;
        color: #000000;
        border: 2px solid #ffffff;
    }
}
