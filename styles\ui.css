/* UI Components and Interface Elements */

/* Navigation UI */
.navigation-ui {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 15;
    display: none; /* Hidden by default, shown when museum is active */
}

.nav-button {
    background: rgba(20, 25, 35, 0.9);
    border: 1px solid #4a5568;
    color: white;
    padding: 10px 15px;
    margin: 5px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 200ms ease;
    font-size: 14px;
    backdrop-filter: blur(10px);
}

.nav-button:hover {
    background: rgba(245, 158, 11, 0.8);
    border-color: #f59e0b;
    transform: translateY(-1px);
}

/* Mini-map */
.mini-map {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 200px;
    height: 150px;
    background: rgba(20, 25, 35, 0.9);
    border: 1px solid #4a5568;
    border-radius: 10px;
    z-index: 15;
    display: none;
    backdrop-filter: blur(10px);
}

.mini-map-title {
    color: #f59e0b;
    font-size: 12px;
    font-weight: bold;
    padding: 8px;
    border-bottom: 1px solid #4a5568;
}

.mini-map-content {
    padding: 10px;
    height: calc(100% - 40px);
    position: relative;
}

.player-dot {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #f59e0b;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.2); }
}

/* Controls help */
.controls-help {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(20, 25, 35, 0.9);
    border: 1px solid #4a5568;
    border-radius: 10px;
    padding: 15px;
    z-index: 15;
    display: none;
    backdrop-filter: blur(10px);
    max-width: 300px;
}

.controls-help h4 {
    color: #f59e0b;
    margin-bottom: 10px;
    font-size: 14px;
}

.controls-help ul {
    list-style: none;
    font-size: 12px;
    line-height: 1.4;
}

.controls-help li {
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
}

.key {
    background: #4a5568;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 11px;
}

/* Progress indicator */
.progress-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(20, 25, 35, 0.9);
    border: 1px solid #4a5568;
    border-radius: 20px;
    padding: 8px 20px;
    z-index: 15;
    display: none;
    backdrop-filter: blur(10px);
}

.progress-bar {
    width: 200px;
    height: 4px;
    background: #4a5568;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 5px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #f59e0b, #d97706);
    width: 0%;
    transition: width 300ms ease;
}

.progress-text {
    font-size: 12px;
    color: #cbd5e0;
    text-align: center;
}

/* Audio controls */
.audio-controls {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 15;
    display: none;
}

.audio-button {
    background: rgba(20, 25, 35, 0.9);
    border: 1px solid #4a5568;
    color: white;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 200ms ease;
    backdrop-filter: blur(10px);
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.audio-button:hover {
    background: rgba(245, 158, 11, 0.8);
    border-color: #f59e0b;
}

.audio-button.muted {
    background: rgba(220, 38, 38, 0.8);
    border-color: #dc2626;
}

/* Settings panel */
.settings-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(20, 25, 35, 0.95);
    border: 1px solid #4a5568;
    border-radius: 15px;
    padding: 30px;
    z-index: 25;
    display: none;
    backdrop-filter: blur(15px);
    max-width: 400px;
    width: 90%;
}

.settings-title {
    color: #f59e0b;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
}

.setting-item {
    margin-bottom: 15px;
}

.setting-label {
    display: block;
    color: #cbd5e0;
    font-size: 14px;
    margin-bottom: 5px;
}

.setting-input {
    width: 100%;
    background: #4a5568;
    border: 1px solid #6b7280;
    border-radius: 6px;
    padding: 8px 12px;
    color: white;
    font-size: 14px;
}

.setting-input:focus {
    outline: none;
    border-color: #f59e0b;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.close-settings {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
}

.close-settings:hover {
    color: #f59e0b;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navigation-ui,
    .mini-map,
    .controls-help,
    .audio-controls {
        display: none !important; /* Hide complex UI on mobile */
    }
    
    .settings-panel {
        width: 95%;
        padding: 20px;
    }
}
